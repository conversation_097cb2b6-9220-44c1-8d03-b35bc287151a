# Admin Setup Guide

## Creating an Admin User

This healthcare website requires an admin user to access the admin panel. Follow these steps to create the initial admin account.

### Prerequisites
- MongoDB database must be running
- Server environment variables must be configured (`.env` file)

### Step 1: Create Admin User
Navigate to the server directory and run the admin seeding script:

```bash
cd server
npm run seed:admin
```

### Step 2: Admin Login Credentials
After running the script, you'll receive the following login credentials:

```
Email: <EMAIL>
Password: Admin123!
```

### Step 3: Access Admin Panel
1. Start the application (both frontend and backend)
2. Go to the login page
3. Use the admin credentials above
4. You'll be automatically redirected to the admin dashboard

### Admin Features
Once logged in as admin, you can access:
- **Admin Dashboard** - System overview and statistics
- **User Management** - View and manage all users
- **Doctor Management** - Approve/reject doctor applications
- **Appointment Monitoring** - Monitor all appointments
- **AI Logs** - View AI interaction logs
- **System Analytics** - Generate reports and analytics

### Security Notes
- **Change the default password** after first login
- The admin user has full system access
- Only create admin accounts for trusted users
- The seeding script will not create duplicate admin users

### Troubleshooting
- If you see "Admin user already exists", you can use the existing credentials
- Make sure MongoDB is running and accessible
- Check your `.env` file for correct database configuration
- Ensure all dependencies are installed (`npm install`)

### Changing Admin Credentials
To change admin credentials:
1. Login to the admin panel
2. Go to your profile settings
3. Update email/password as needed
4. Or manually update in the database 