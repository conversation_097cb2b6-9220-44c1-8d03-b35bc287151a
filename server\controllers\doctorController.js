import User from '../models/User.js';
import Appointment from '../models/Appointment.js';
import { catchAsync } from '../utils/catchAsync.js';
import { AppError } from '../utils/appError.js';

// Get doctor dashboard data
export const getDoctorDashboard = catchAsync(async (req, res) => {
  const doctorId = req.user.id;
  
  const todayAppointments = await Appointment.find({
    doctor: doctorId,
    dateTime: { 
      $gte: new Date(new Date().setHours(0,0,0,0)),
      $lt: new Date(new Date().setHours(23,59,59,999))
    }
  })
    .populate('patient', 'firstName lastName')
    .sort({ dateTime: 1 });

  const upcomingAppointments = await Appointment.find({
    doctor: doctorId,
    dateTime: { $gte: new Date() },
    status: { $ne: 'cancelled' }
  })
    .populate('patient', 'firstName lastName')
    .sort({ dateTime: 1 })
    .limit(5);

  const appointmentStats = await Appointment.aggregate([
    { $match: { doctor: req.user.id } },
    { $group: { _id: '$status', count: { $sum: 1 } } }
  ]);

  const stats = {
    todayAppointments: todayAppointments.length,
    upcomingAppointments: upcomingAppointments.length,
    totalPatients: 0,
    completedAppointments: appointmentStats.find(s => s._id === 'completed')?.count || 0
  };

  res.status(200).json({
    success: true,
    data: { stats, todayAppointments, upcomingAppointments }
  });
});

// Get doctor appointments
export const getDoctorAppointments = catchAsync(async (req, res) => {
  const { page = 1, limit = 10, status } = req.query;
  const filter = { doctor: req.user.id };
  
  if (status) filter.status = status;

  const appointments = await Appointment.find(filter)
    .populate('patient', 'firstName lastName email phone')
    .sort({ dateTime: -1 })
    .skip((page - 1) * limit)
    .limit(parseInt(limit));

  const total = await Appointment.countDocuments(filter);

  res.status(200).json({
    success: true,
    data: {
      appointments,
      pagination: { page: parseInt(page), limit: parseInt(limit), total, pages: Math.ceil(total / limit) }
    }
  });
});

// Get doctor patients
export const getDoctorPatients = catchAsync(async (req, res) => {
  const { page = 1, limit = 10 } = req.query;
  
  const patientIds = await Appointment.distinct('patient', { doctor: req.user.id });
  
  const patients = await User.find({ _id: { $in: patientIds } })
    .select('firstName lastName email phone')
    .sort({ firstName: 1 })
    .skip((page - 1) * limit)
    .limit(parseInt(limit));

  const total = patientIds.length;

  res.status(200).json({
    success: true,
    data: {
      patients,
      pagination: { page: parseInt(page), limit: parseInt(limit), total, pages: Math.ceil(total / limit) }
    }
  });
});

// Get patient details
export const getPatientDetails = catchAsync(async (req, res, next) => {
  const { id: patientId } = req.params;
  const doctorId = req.user.id;

  const hasAppointment = await Appointment.findOne({
    doctor: doctorId,
    patient: patientId
  });

  if (!hasAppointment) {
    return next(new AppError('You do not have permission to view this patient', 403));
  }

  const patient = await User.findById(patientId)
    .select('firstName lastName email phone dateOfBirth gender');

  if (!patient) {
    return next(new AppError('Patient not found', 404));
  }

  const appointments = await Appointment.find({
    doctor: doctorId,
    patient: patientId
  }).sort({ dateTime: -1 });

  res.status(200).json({
    success: true,
    data: { patient, appointments }
  });
});

// Update appointment status
export const updateAppointmentStatus = catchAsync(async (req, res, next) => {
  const { id: appointmentId } = req.params;
  const { status } = req.body;

  const appointment = await Appointment.findOneAndUpdate(
    { _id: appointmentId, doctor: req.user.id },
    { status, updatedAt: new Date() },
    { new: true, runValidators: true }
  ).populate('patient', 'firstName lastName');

  if (!appointment) {
    return next(new AppError('Appointment not found or unauthorized', 404));
  }

  res.status(200).json({
    success: true,
    data: { appointment }
  });
});

// Add appointment notes
export const addAppointmentNotes = catchAsync(async (req, res, next) => {
  const { id: appointmentId } = req.params;
  const { notes } = req.body;

  const appointment = await Appointment.findOneAndUpdate(
    { _id: appointmentId, doctor: req.user.id },
    { notes, updatedAt: new Date() },
    { new: true, runValidators: true }
  ).populate('patient', 'firstName lastName');

  if (!appointment) {
    return next(new AppError('Appointment not found or unauthorized', 404));
  }

  res.status(200).json({
    success: true,
    data: { appointment }
  });
});

// Get doctor profile
export const getDoctorProfile = catchAsync(async (req, res, next) => {
  const doctor = await User.findById(req.user.id).select('-password');

  if (!doctor) {
    return next(new AppError('Doctor not found', 404));
  }

  res.status(200).json({
    success: true,
    data: { doctor }
  });
});

// Update doctor profile
export const updateDoctorProfile = catchAsync(async (req, res) => {
  const allowedFields = ['firstName', 'lastName', 'phone', 'specialty', 'bio'];
  const updateData = {};
  
  Object.keys(req.body).forEach(key => {
    if (allowedFields.includes(key)) {
      updateData[key] = req.body[key];
    }
  });

  const doctor = await User.findByIdAndUpdate(
    req.user.id,
    updateData,
    { new: true, runValidators: true }
  ).select('-password');

  res.status(200).json({
    success: true,
    data: { doctor }
  });
});

// Get availability settings
export const getAvailabilitySettings = catchAsync(async (req, res) => {
  const doctor = await User.findById(req.user.id).select('availability');
  
  res.status(200).json({
    success: true,
    data: { availability: doctor.availability || {} }
  });
});

// Update availability
export const updateAvailability = catchAsync(async (req, res) => {
  const doctor = await User.findByIdAndUpdate(
    req.user.id,
    { availability: req.body.availability },
    { new: true, runValidators: true }
  ).select('availability');

  res.status(200).json({
    success: true,
    data: { availability: doctor.availability }
  });
});

// Get doctor schedule
export const getDoctorSchedule = catchAsync(async (req, res) => {
  const { startDate, endDate } = req.query;
  const filter = { doctor: req.user.id };
  
  if (startDate || endDate) {
    filter.dateTime = {};
    if (startDate) filter.dateTime.$gte = new Date(startDate);
    if (endDate) filter.dateTime.$lte = new Date(endDate);
  }

  const schedule = await Appointment.find(filter)
    .populate('patient', 'firstName lastName')
    .sort({ dateTime: 1 });

  res.status(200).json({
    success: true,
    data: { schedule }
  });
});

// Update doctor schedule
export const updateDoctorSchedule = catchAsync(async (req, res) => {
  const doctor = await User.findByIdAndUpdate(
    req.user.id,
    { schedule: req.body.schedule },
    { new: true, runValidators: true }
  ).select('schedule');

  res.status(200).json({
    success: true,
    data: { schedule: doctor.schedule }
  });
}); 