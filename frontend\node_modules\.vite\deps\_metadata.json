{"hash": "dd7a48e3", "configHash": "30dd9bcf", "lockfileHash": "3d602f49", "browserHash": "aa68a2ad", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "b461286e", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "e937cfe3", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "95d5f810", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "957b8bca", "needsInterop": true}, "@heroicons/react/24/outline": {"src": "../../@heroicons/react/24/outline/esm/index.js", "file": "@heroicons_react_24_outline.js", "fileHash": "0450d36e", "needsInterop": false}, "@heroicons/react/24/solid": {"src": "../../@heroicons/react/24/solid/esm/index.js", "file": "@heroicons_react_24_solid.js", "fileHash": "5db11c41", "needsInterop": false}, "axios": {"src": "../../axios/index.js", "file": "axios.js", "fileHash": "66167458", "needsInterop": false}, "framer-motion": {"src": "../../framer-motion/dist/es/index.mjs", "file": "framer-motion.js", "fileHash": "9a595fb5", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "08300328", "needsInterop": true}, "react-router-dom": {"src": "../../react-router-dom/dist/index.mjs", "file": "react-router-dom.js", "fileHash": "163f1181", "needsInterop": false}}, "chunks": {"chunk-HE4GKDYE": {"file": "chunk-HE4GKDYE.js"}, "chunk-MJNCUEZK": {"file": "chunk-MJNCUEZK.js"}, "chunk-UGC3UZ7L": {"file": "chunk-UGC3UZ7L.js"}, "chunk-G3PMV62Z": {"file": "chunk-G3PMV62Z.js"}}}