import axios from 'axios';
import AILog from '../models/AILog.js';

class AIService {
  constructor() {
    this.apiKey = process.env.OPENROUTER_API_KEY;
    this.apiUrl = process.env.OPENROUTER_API_URL || 'https://openrouter.ai/api/v1/chat/completions';
    this.model = process.env.OPENROUTER_MODEL || 'anthropic/claude-instant-v1';
    this.maxTokens = 1000;
    this.temperature = 0.7;
  }

  /**
   * Analyze symptoms and recommend medical specialty
   * @param {string} symptoms - Patient's symptom description
   * @param {string} patientId - Patient's ID
   * @param {Object} requestMetadata - Request metadata (IP, user agent, etc.)
   * @returns {Object} Analysis result with specialty recommendation
   */
  async analyzeSymptoms(symptoms, patientId, requestMetadata = {}) {
    try {
      const startTime = Date.now();
      const requestId = this.generateRequestId();

      // Prepare the system prompt for medical analysis
      const systemPrompt = this.buildSystemPrompt();
      
      // Prepare the user prompt with symptoms
      const userPrompt = this.buildUserPrompt(symptoms);

      // Make API request to OpenRouter
      const response = await this.makeAPIRequest(systemPrompt, userPrompt);
      
      const responseTime = Date.now() - startTime;

      // Parse the AI response
      const analysis = this.parseAIResponse(response.data);

      // Create log entry
      const logData = await this.createLogEntry({
        patientId,
        symptoms,
        analysis,
        requestId,
        requestMetadata,
        responseTime,
        tokenUsage: response.data.usage || {},
        apiCost: this.calculateCost(response.data.usage || {})
      });

      return {
        success: true,
        requestId,
        analysis: {
          recommendedSpecialty: analysis.recommendedSpecialty,
          confidence: analysis.confidence,
          alternativeSpecialties: analysis.alternativeSpecialties,
          urgencyLevel: analysis.urgencyLevel,
          reasoning: analysis.reasoning,
          suggestedQuestions: analysis.suggestedQuestions,
          redFlags: analysis.redFlags
        },
        metadata: {
          responseTime,
          model: this.model,
          logId: logData._id
        }
      };
    } catch (error) {
      console.error('AI Analysis Error:', error);
      
      // Log the error
      await this.logError(patientId, symptoms, error, requestMetadata);
      
      return {
        success: false,
        error: 'Failed to analyze symptoms',
        fallback: this.getFallbackRecommendation(symptoms)
      };
    }
  }

  /**
   * Build system prompt for medical analysis
   * @returns {string} System prompt
   */
  buildSystemPrompt() {
    return `You are a medical AI assistant that helps analyze patient symptoms and recommend appropriate medical specialties. Your role is to:

1. Analyze the symptoms described by the patient
2. Recommend the most appropriate medical specialty for consultation
3. Provide a confidence score (0-1) for your recommendation
4. Suggest alternative specialties if applicable
5. Assess urgency level (low, medium, high, critical)
6. Provide clear reasoning for your recommendation
7. Suggest relevant questions the patient should ask the doctor
8. Identify any red flags that require immediate attention

Important guidelines:
- Always recommend seeking professional medical advice
- Never provide definitive diagnoses
- Be conservative with urgency assessments
- Focus on specialty matching rather than diagnosis
- Consider patient safety first
- Provide educational information, not medical advice

Medical specialties available:
General Practice, Cardiology, Dermatology, Endocrinology, Gastroenterology, Hematology, Infectious Disease, Nephrology, Neurology, Oncology, Orthopedics, Pediatrics, Psychiatry, Pulmonology, Radiology, Rheumatology, Urology, Gynecology, Ophthalmology, ENT, Emergency Medicine, Anesthesiology, Pathology, Surgery, Plastic Surgery, Neurosurgery, Orthopedic Surgery, Cardiac Surgery

Respond in JSON format with the following structure:
{
  "recommendedSpecialty": "Primary specialty recommendation",
  "confidence": 0.8,
  "alternativeSpecialties": [
    {"specialty": "Alternative 1", "confidence": 0.6},
    {"specialty": "Alternative 2", "confidence": 0.4}
  ],
  "urgencyLevel": "medium",
  "reasoning": "Clear explanation of why this specialty is recommended",
  "suggestedQuestions": ["Question 1", "Question 2", "Question 3"],
  "redFlags": ["Any concerning symptoms that need immediate attention"]
}`;
  }

  /**
   * Build user prompt with symptoms
   * @param {string} symptoms - Patient symptoms
   * @returns {string} User prompt
   */
  buildUserPrompt(symptoms) {
    return `Please analyze the following symptoms and provide a medical specialty recommendation:

Patient Symptoms: "${symptoms}"

Please analyze these symptoms and recommend the most appropriate medical specialty for consultation. Consider the symptoms holistically and provide your recommendation in the specified JSON format.`;
  }

  /**
   * Make API request to OpenRouter
   * @param {string} systemPrompt - System prompt
   * @param {string} userPrompt - User prompt
   * @returns {Object} API response
   */
  async makeAPIRequest(systemPrompt, userPrompt) {
    const headers = {
      'Authorization': `Bearer ${this.apiKey}`,
      'Content-Type': 'application/json',
      'HTTP-Referer': 'https://medical-appointment-system.com',
      'X-Title': 'Medical Appointment System'
    };

    const data = {
      model: this.model,
      messages: [
        {
          role: 'system',
          content: systemPrompt
        },
        {
          role: 'user',
          content: userPrompt
        }
      ],
      max_tokens: this.maxTokens,
      temperature: this.temperature,
      top_p: 1,
      frequency_penalty: 0,
      presence_penalty: 0
    };

    const response = await axios.post(this.apiUrl, data, { headers });
    return response;
  }

  /**
   * Parse AI response from OpenRouter
   * @param {Object} responseData - Raw API response
   * @returns {Object} Parsed analysis
   */
  parseAIResponse(responseData) {
    try {
      const content = responseData.choices[0].message.content;
      
      // Try to parse JSON from the response
      let jsonMatch = content.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0]);
        
        // Validate and sanitize the response
        return {
          recommendedSpecialty: parsed.recommendedSpecialty || 'General Practice',
          confidence: Math.min(Math.max(parsed.confidence || 0.5, 0), 1),
          alternativeSpecialties: (parsed.alternativeSpecialties || []).slice(0, 3),
          urgencyLevel: ['low', 'medium', 'high', 'critical'].includes(parsed.urgencyLevel) 
            ? parsed.urgencyLevel : 'medium',
          reasoning: parsed.reasoning || 'General medical consultation recommended',
          suggestedQuestions: (parsed.suggestedQuestions || []).slice(0, 5),
          redFlags: (parsed.redFlags || []).slice(0, 5)
        };
      }
      
      // Fallback if JSON parsing fails
      return this.createFallbackAnalysis(content);
    } catch (error) {
      console.error('Error parsing AI response:', error);
      return this.createFallbackAnalysis();
    }
  }

  /**
   * Create fallback analysis when AI parsing fails
   * @param {string} content - Raw content
   * @returns {Object} Fallback analysis
   */
  createFallbackAnalysis(content = '') {
    return {
      recommendedSpecialty: 'General Practice',
      confidence: 0.5,
      alternativeSpecialties: [
        { specialty: 'Internal Medicine', confidence: 0.4 }
      ],
      urgencyLevel: 'medium',
      reasoning: 'Based on the symptoms provided, a general medical consultation is recommended for proper evaluation.',
      suggestedQuestions: [
        'How long have you been experiencing these symptoms?',
        'Are there any factors that make the symptoms better or worse?',
        'Do you have any relevant medical history?'
      ],
      redFlags: []
    };
  }

  /**
   * Create log entry for AI analysis
   * @param {Object} logData - Log data
   * @returns {Object} Created log entry
   */
  async createLogEntry(logData) {
    try {
      const aiLog = new AILog({
        patient: logData.patientId,
        symptoms: logData.symptoms,
        aiModel: this.model,
        modelVersion: '1.0',
        prediction: {
          recommendedSpecialty: logData.analysis.recommendedSpecialty,
          confidence: logData.analysis.confidence,
          alternativeSpecialties: logData.analysis.alternativeSpecialties,
          urgencyLevel: logData.analysis.urgencyLevel,
          reasoning: logData.analysis.reasoning,
          suggestedQuestions: logData.analysis.suggestedQuestions,
          redFlags: logData.analysis.redFlags
        },
        requestMetadata: {
          requestId: logData.requestId,
          userAgent: logData.requestMetadata.userAgent,
          ipAddress: logData.requestMetadata.ipAddress,
          sessionId: logData.requestMetadata.sessionId,
          timestamp: new Date()
        },
        responseMetadata: {
          responseTime: logData.responseTime,
          tokenUsage: logData.tokenUsage,
          apiCost: logData.apiCost,
          modelTemperature: this.temperature,
          maxTokens: this.maxTokens
        }
      });

      return await aiLog.save();
    } catch (error) {
      console.error('Error creating AI log entry:', error);
      throw error;
    }
  }

  /**
   * Log error for failed AI analysis
   * @param {string} patientId - Patient ID
   * @param {string} symptoms - Symptoms
   * @param {Error} error - Error object
   * @param {Object} requestMetadata - Request metadata
   */
  async logError(patientId, symptoms, error, requestMetadata) {
    try {
      const requestId = this.generateRequestId();
      
      const aiLog = new AILog({
        patient: patientId,
        symptoms,
        aiModel: this.model,
        modelVersion: '1.0',
        prediction: {
          recommendedSpecialty: 'General Practice',
          confidence: 0,
          urgencyLevel: 'medium',
          reasoning: 'AI analysis failed, general consultation recommended'
        },
        requestMetadata: {
          requestId,
          userAgent: requestMetadata.userAgent,
          ipAddress: requestMetadata.ipAddress,
          sessionId: requestMetadata.sessionId,
          timestamp: new Date()
        },
        responseMetadata: {
          responseTime: 0,
          tokenUsage: {},
          apiCost: 0
        },
        status: 'flagged',
        flags: [{
          type: 'system_error',
          reason: `AI analysis failed: ${error.message}`,
          flaggedAt: new Date()
        }]
      });

      await aiLog.save();
    } catch (logError) {
      console.error('Error logging AI failure:', logError);
    }
  }

  /**
   * Get fallback recommendation when AI fails
   * @param {string} symptoms - Symptoms
   * @returns {Object} Fallback recommendation
   */
  getFallbackRecommendation(symptoms) {
    const lowerSymptoms = symptoms.toLowerCase();
    
    // Simple keyword-based fallback
    if (lowerSymptoms.includes('chest') || lowerSymptoms.includes('heart')) {
      return { specialty: 'Cardiology', reason: 'Chest-related symptoms' };
    } else if (lowerSymptoms.includes('skin') || lowerSymptoms.includes('rash')) {
      return { specialty: 'Dermatology', reason: 'Skin-related symptoms' };
    } else if (lowerSymptoms.includes('stomach') || lowerSymptoms.includes('digestive')) {
      return { specialty: 'Gastroenterology', reason: 'Digestive symptoms' };
    } else {
      return { specialty: 'General Practice', reason: 'General medical consultation' };
    }
  }

  /**
   * Calculate API cost based on token usage
   * @param {Object} usage - Token usage from API
   * @returns {number} Estimated cost
   */
  calculateCost(usage) {
    // OpenRouter pricing varies by model, this is an estimate
    const inputCostPer1K = 0.0001; // $0.0001 per 1K input tokens
    const outputCostPer1K = 0.0002; // $0.0002 per 1K output tokens
    
    const inputCost = (usage.prompt_tokens || 0) * inputCostPer1K / 1000;
    const outputCost = (usage.completion_tokens || 0) * outputCostPer1K / 1000;
    
    return inputCost + outputCost;
  }

  /**
   * Generate unique request ID
   * @returns {string} Request ID
   */
  generateRequestId() {
    return `ai_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
  }

  /**
   * Get AI analytics
   * @param {Date} startDate - Start date
   * @param {Date} endDate - End date
   * @returns {Object} Analytics data
   */
  async getAnalytics(startDate, endDate) {
    try {
      return await AILog.getAnalytics(startDate, endDate);
    } catch (error) {
      console.error('Error getting AI analytics:', error);
      throw error;
    }
  }

  /**
   * Get model performance metrics
   * @param {string} modelName - Model name
   * @param {Date} startDate - Start date
   * @param {Date} endDate - End date
   * @returns {Object} Performance metrics
   */
  async getModelMetrics(modelName, startDate, endDate) {
    try {
      return await AILog.getModelMetrics(modelName, startDate, endDate);
    } catch (error) {
      console.error('Error getting model metrics:', error);
      throw error;
    }
  }
}

export default new AIService(); 