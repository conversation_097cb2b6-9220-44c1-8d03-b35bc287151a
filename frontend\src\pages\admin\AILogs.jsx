﻿/* eslint-disable no-unused-vars */
import React, { useState, useEffect } from 'react';
import { 
  ChartBarIcon, 
  ExclamationTriangleIcon, 
  CheckCircleIcon, 
  ClockIcon,
  UserIcon,
  BeakerIcon,
  ExclamationCircleIcon,
  ShieldCheckIcon
} from '@heroicons/react/24/outline';
import { motion } from 'framer-motion';
import adminService from '../../services/adminService';

const AILogs = () => {
  const [logs, setLogs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState('all');
  const [selectedLog, setSelectedLog] = useState(null);
  const [showDetails, setShowDetails] = useState(false);

  useEffect(() => {
    fetchAILogs();
  }, []);

  const fetchAILogs = async () => {
    try {
      const response = await adminService.getAISystemLogs();
      console.log('AI Logs Response:', response.data);
      setLogs(response.data || []);
    } catch (error) {
      console.error('Error fetching AI logs:', error);
    } finally {
      setLoading(false);
    }
  };

  const filteredLogs = logs.filter(log => {
    if (filter === 'all') return true;
    if (filter === 'high-confidence') return log.prediction?.confidence >= 0.8;
    if (filter === 'low-confidence') return log.prediction?.confidence < 0.5;
    if (filter === 'urgent') return log.prediction?.urgencyLevel === 'high' || log.prediction?.urgencyLevel === 'critical';
    return log.status === filter;
  });

  const getStatusIcon = (status) => {
    switch (status) {
      case 'used':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case 'pending':
        return <ClockIcon className="h-5 w-5 text-yellow-500" />;
      case 'flagged':
        return <ExclamationTriangleIcon className="h-5 w-5 text-red-500" />;
      case 'validated':
        return <ShieldCheckIcon className="h-5 w-5 text-blue-500" />;
      default:
        return <BeakerIcon className="h-5 w-5 text-gray-500" />;
    }
  };

  const getUrgencyColor = (urgencyLevel) => {
    switch (urgencyLevel) {
      case 'critical':
        return 'bg-red-100 text-red-800';
      case 'high':
        return 'bg-orange-100 text-orange-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'low':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getConfidenceColor = (confidence) => {
    if (confidence >= 0.8) return 'text-green-600';
    if (confidence >= 0.6) return 'text-yellow-600';
    return 'text-red-600';
  };

  const showLogDetails = (log) => {
    setSelectedLog(log);
    setShowDetails(true);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">AI System Logs</h1>
        <p className="text-gray-600">Monitor AI analysis activities and performance</p>
      </div>

      {/* Filter Buttons */}
      <div className="mb-6 flex flex-wrap gap-2">
        <button
          onClick={() => setFilter('all')}
          className={`px-4 py-2 rounded-lg ${filter === 'all' ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700'}`}
        >
          All Logs ({logs.length})
        </button>
        <button
          onClick={() => setFilter('high-confidence')}
          className={`px-4 py-2 rounded-lg ${filter === 'high-confidence' ? 'bg-green-500 text-white' : 'bg-gray-200 text-gray-700'}`}
        >
          High Confidence
        </button>
        <button
          onClick={() => setFilter('low-confidence')}
          className={`px-4 py-2 rounded-lg ${filter === 'low-confidence' ? 'bg-red-500 text-white' : 'bg-gray-200 text-gray-700'}`}
        >
          Low Confidence
        </button>
        <button
          onClick={() => setFilter('urgent')}
          className={`px-4 py-2 rounded-lg ${filter === 'urgent' ? 'bg-orange-500 text-white' : 'bg-gray-200 text-gray-700'}`}
        >
          Urgent Cases
        </button>
        <button
          onClick={() => setFilter('used')}
          className={`px-4 py-2 rounded-lg ${filter === 'used' ? 'bg-purple-500 text-white' : 'bg-gray-200 text-gray-700'}`}
        >
          Used
        </button>
      </div>

      {/* Main Content */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Patient & Symptoms
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  AI Prediction
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Confidence
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Urgency
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Date
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredLogs.map((log, index) => (
                <motion.tr
                  key={log._id || index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="hover:bg-gray-50"
                >
                  <td className="px-6 py-4">
                    <div className="flex items-center">
                      <UserIcon className="h-5 w-5 text-gray-400 mr-2" />
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {log.patient?.firstName || 'Unknown'} {log.patient?.lastName || 'Patient'}
                        </div>
                        <div className="text-sm text-gray-500 max-w-xs truncate">
                          {log.symptoms ? log.symptoms.substring(0, 80) + '...' : 'No symptoms recorded'}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-900">
                      {log.prediction?.recommendedSpecialty || 'No recommendation'}
                    </div>
                    <div className="text-sm text-gray-500">
                      {log.aiModel || 'Unknown'} v{log.modelVersion || '1.0'}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`text-sm font-medium ${getConfidenceColor(log.prediction?.confidence)}`}>
                      {log.prediction?.confidence ? (log.prediction.confidence * 100).toFixed(1) + '%' : 'N/A'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${getUrgencyColor(log.prediction?.urgencyLevel)}`}>
                      {log.prediction?.urgencyLevel || 'unknown'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      {getStatusIcon(log.status)}
                      <span className="ml-2 text-sm font-medium capitalize">
                        {log.status || 'unknown'}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {new Date(log.createdAt).toLocaleDateString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <button
                      onClick={() => showLogDetails(log)}
                      className="text-blue-600 hover:text-blue-900"
                    >
                      View Details
                    </button>
                  </td>
                </motion.tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Empty State */}
      {filteredLogs.length === 0 && (
        <div className="text-center py-12">
          <ChartBarIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No AI logs found</h3>
          <p className="mt-1 text-sm text-gray-500">
            No AI system logs match the current filter.
          </p>
        </div>
      )}

      {/* Details Modal */}
      {showDetails && selectedLog && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900">AI Analysis Details</h3>
                <button
                  onClick={() => setShowDetails(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ×
                </button>
              </div>
              
              <div className="space-y-6">
                {/* Patient Information */}
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Patient Information</h4>
                  <div className="bg-gray-50 p-3 rounded">
                    <p><strong>Name:</strong> {selectedLog.patient?.firstName} {selectedLog.patient?.lastName}</p>
                    <p><strong>Email:</strong> {selectedLog.patient?.email}</p>
                  </div>
                </div>

                {/* Symptoms */}
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Symptoms</h4>
                  <div className="bg-gray-50 p-3 rounded">
                    <p>{selectedLog.symptoms}</p>
                  </div>
                </div>

                {/* AI Prediction */}
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">AI Prediction</h4>
                  <div className="bg-blue-50 p-3 rounded">
                    <p><strong>Recommended Specialty:</strong> {selectedLog.prediction?.recommendedSpecialty}</p>
                    <p><strong>Confidence:</strong> {selectedLog.prediction?.confidence ? (selectedLog.prediction.confidence * 100).toFixed(1) + '%' : 'N/A'}</p>
                    <p><strong>Urgency Level:</strong> {selectedLog.prediction?.urgencyLevel}</p>
                    {selectedLog.prediction?.reasoning && (
                      <p><strong>Reasoning:</strong> {selectedLog.prediction.reasoning}</p>
                    )}
                    {selectedLog.prediction?.redFlags && selectedLog.prediction.redFlags.length > 0 && (
                      <p><strong>Red Flags:</strong> {selectedLog.prediction.redFlags.join(', ')}</p>
                    )}
                  </div>
                </div>

                {/* Alternative Specialties */}
                {selectedLog.prediction?.alternativeSpecialties && selectedLog.prediction.alternativeSpecialties.length > 0 && (
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Alternative Specialties</h4>
                    <div className="bg-yellow-50 p-3 rounded">
                      {selectedLog.prediction.alternativeSpecialties.map((alt, index) => (
                        <p key={index}>
                          <strong>{alt.specialty}:</strong> {(alt.confidence * 100).toFixed(1)}%
                        </p>
                      ))}
                    </div>
                  </div>
                )}

                {/* Metadata */}
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Metadata</h4>
                  <div className="bg-gray-50 p-3 rounded">
                    <p><strong>AI Model:</strong> {selectedLog.aiModel} v{selectedLog.modelVersion}</p>
                    <p><strong>Status:</strong> {selectedLog.status}</p>
                    <p><strong>Response Time:</strong> {selectedLog.responseMetadata?.responseTime}ms</p>
                    <p><strong>Created:</strong> {new Date(selectedLog.createdAt).toLocaleString()}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AILogs;
