import mongoose from 'mongoose';
import User from '../models/User.js';
import Doctor from '../models/Doctor.js';
import Appointment from '../models/Appointment.js';
import AILog from '../models/AILog.js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const seedDatabase = async () => {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/healthcare-app');
    console.log('Connected to MongoDB');

    // Clear existing data (optional - comment out if you want to keep existing data)
    console.log('Clearing existing data...');
    await User.deleteMany({ email: { $not: /admin@healthcare\.com/ } }); // Keep admin user
    await Doctor.deleteMany({});
    await Appointment.deleteMany({});
    await AILog.deleteMany({});

    console.log('Creating sample data...');

    // 1. Create Sample Patients
    const patients = [
      {
        firstName: 'John',
        lastName: 'Smith',
        email: '<EMAIL>',
        password: 'password123',
        role: 'patient',
        phone: '+15551234567',
        dateOfBirth: new Date('1990-05-15'),
        gender: 'male',
        address: {
          street: '123 Main St',
          city: 'New York',
          state: 'NY',
          zipCode: '10001',
          country: 'United States'
        },
        isActive: true,
        isEmailVerified: true
      },
      {
        firstName: 'Sarah',
        lastName: 'Johnson',
        email: '<EMAIL>',
        password: 'password123',
        role: 'patient',
        phone: '+15551234568',
        dateOfBirth: new Date('1985-09-22'),
        gender: 'female',
        address: {
          street: '456 Oak Ave',
          city: 'Los Angeles',
          state: 'CA',
          zipCode: '90210',
          country: 'United States'
        },
        isActive: true,
        isEmailVerified: true
      },
      {
        firstName: 'Michael',
        lastName: 'Brown',
        email: '<EMAIL>',
        password: 'password123',
        role: 'patient',
        phone: '+15551234569',
        dateOfBirth: new Date('1978-12-03'),
        gender: 'male',
        address: {
          street: '789 Pine Rd',
          city: 'Chicago',
          state: 'IL',
          zipCode: '60601',
          country: 'United States'
        },
        isActive: true,
        isEmailVerified: true
      },
      {
        firstName: 'Emily',
        lastName: 'Davis',
        email: '<EMAIL>',
        password: 'password123',
        role: 'patient',
        phone: '+15551234570',
        dateOfBirth: new Date('1995-07-18'),
        gender: 'female',
        address: {
          street: '321 Elm St',
          city: 'Houston',
          state: 'TX',
          zipCode: '77001',
          country: 'United States'
        },
        isActive: true,
        isEmailVerified: true
      },
      {
        firstName: 'David',
        lastName: 'Wilson',
        email: '<EMAIL>',
        password: 'password123',
        role: 'patient',
        phone: '+15551234571',
        dateOfBirth: new Date('1988-03-25'),
        gender: 'male',
        address: {
          street: '654 Maple Dr',
          city: 'Phoenix',
          state: 'AZ',
          zipCode: '85001',
          country: 'United States'
        },
        isActive: true,
        isEmailVerified: true
      }
    ];

    const createdPatients = await User.insertMany(patients);
    console.log(`✅ Created ${createdPatients.length} patients`);

    // 2. Create Sample Doctor Users
    const doctorUsers = [
      {
        firstName: 'Dr. Robert',
        lastName: 'Anderson',
        email: '<EMAIL>',
        password: 'password123',
        role: 'doctor',
        phone: '+15559876543',
        isActive: true,
        isEmailVerified: true
      },
      {
        firstName: 'Dr. Lisa',
        lastName: 'Martinez',
        email: '<EMAIL>',
        password: 'password123',
        role: 'doctor',
        phone: '+15559876544',
        isActive: true,
        isEmailVerified: true
      },
      {
        firstName: 'Dr. James',
        lastName: 'Taylor',
        email: '<EMAIL>',
        password: 'password123',
        role: 'doctor',
        phone: '+15559876545',
        isActive: true,
        isEmailVerified: true
      },
      {
        firstName: 'Dr. Maria',
        lastName: 'Garcia',
        email: '<EMAIL>',
        password: 'password123',
        role: 'doctor',
        phone: '+15559876546',
        isActive: true,
        isEmailVerified: true
      },
      {
        firstName: 'Dr. William',
        lastName: 'Lee',
        email: '<EMAIL>',
        password: 'password123',
        role: 'doctor',
        phone: '+15559876547',
        isActive: true,
        isEmailVerified: true
      }
    ];

    const createdDoctorUsers = await User.insertMany(doctorUsers);
    console.log(`✅ Created ${createdDoctorUsers.length} doctor users`);

    // 3. Create Doctor Profiles
    const doctorProfiles = [
      {
        user: createdDoctorUsers[0]._id,
        specialization: 'Cardiology',
        licenseNumber: 'MD12345',
        experience: 15,
        education: [
          {
            degree: 'MD',
            institution: 'Harvard Medical School',
            year: 2008
          }
        ],
        certifications: ['Board Certified Cardiologist', 'ACLS Certified'],
        about: 'Experienced cardiologist specializing in heart disease prevention and treatment.',
        availability: {
          monday: { start: '09:00', end: '17:00', available: true },
          tuesday: { start: '09:00', end: '17:00', available: true },
          wednesday: { start: '09:00', end: '17:00', available: true },
          thursday: { start: '09:00', end: '17:00', available: true },
          friday: { start: '09:00', end: '15:00', available: true },
          saturday: { available: false },
          sunday: { available: false }
        },
        consultationFee: 200,
        isVerified: true,
        status: 'active'
      },
      {
        user: createdDoctorUsers[1]._id,
        specialization: 'Pediatrics',
        licenseNumber: 'MD12346',
        experience: 12,
        education: [
          {
            degree: 'MD',
            institution: 'Johns Hopkins University',
            year: 2011
          }
        ],
        certifications: ['Board Certified Pediatrician', 'PALS Certified'],
        about: 'Dedicated pediatrician focused on comprehensive child healthcare.',
        availability: {
          monday: { start: '08:00', end: '16:00', available: true },
          tuesday: { start: '08:00', end: '16:00', available: true },
          wednesday: { start: '08:00', end: '16:00', available: true },
          thursday: { start: '08:00', end: '16:00', available: true },
          friday: { start: '08:00', end: '14:00', available: true },
          saturday: { available: false },
          sunday: { available: false }
        },
        consultationFee: 150,
        isVerified: true,
        status: 'active'
      },
      {
        user: createdDoctorUsers[2]._id,
        specialization: 'Dermatology',
        licenseNumber: 'MD12347',
        experience: 10,
        education: [
          {
            degree: 'MD',
            institution: 'Stanford University School of Medicine',
            year: 2013
          }
        ],
        certifications: ['Board Certified Dermatologist'],
        about: 'Skilled dermatologist specializing in skin conditions and cosmetic procedures.',
        availability: {
          monday: { start: '10:00', end: '18:00', available: true },
          tuesday: { start: '10:00', end: '18:00', available: true },
          wednesday: { start: '10:00', end: '18:00', available: true },
          thursday: { start: '10:00', end: '18:00', available: true },
          friday: { start: '10:00', end: '16:00', available: true },
          saturday: { available: false },
          sunday: { available: false }
        },
        consultationFee: 180,
        isVerified: true,
        status: 'active'
      },
      {
        user: createdDoctorUsers[3]._id,
        specialization: 'Orthopedics',
        licenseNumber: 'MD12348',
        experience: 18,
        education: [
          {
            degree: 'MD',
            institution: 'Mayo Clinic Alix School of Medicine',
            year: 2005
          }
        ],
        certifications: ['Board Certified Orthopedic Surgeon'],
        about: 'Expert orthopedic surgeon with extensive experience in joint replacement.',
        availability: {
          monday: { start: '07:00', end: '15:00', available: true },
          tuesday: { start: '07:00', end: '15:00', available: true },
          wednesday: { start: '07:00', end: '15:00', available: true },
          thursday: { start: '07:00', end: '15:00', available: true },
          friday: { start: '07:00', end: '13:00', available: true },
          saturday: { available: false },
          sunday: { available: false }
        },
        consultationFee: 250,
        isVerified: true,
        status: 'active'
      },
      {
        user: createdDoctorUsers[4]._id,
        specialization: 'Neurology',
        licenseNumber: 'MD12349',
        experience: 14,
        education: [
          {
            degree: 'MD',
            institution: 'University of California, San Francisco',
            year: 2009
          }
        ],
        certifications: ['Board Certified Neurologist'],
        about: 'Neurologist specializing in brain and nervous system disorders.',
        availability: {
          monday: { start: '09:00', end: '17:00', available: true },
          tuesday: { start: '09:00', end: '17:00', available: true },
          wednesday: { start: '09:00', end: '17:00', available: true },
          thursday: { start: '09:00', end: '17:00', available: true },
          friday: { start: '09:00', end: '15:00', available: true },
          saturday: { available: false },
          sunday: { available: false }
        },
        consultationFee: 220,
        isVerified: true,
        status: 'active'
      }
    ];

    const createdDoctors = await Doctor.insertMany(doctorProfiles);
    console.log(`✅ Created ${createdDoctors.length} doctor profiles`);

    // 4. Create Sample Appointments
    const appointments = [
      {
        user: createdPatients[0]._id,
        doctor: createdDoctors[0]._id,
        appointmentDate: new Date('2024-01-15T10:00:00Z'),
        appointmentTime: '10:00 AM',
        type: 'consultation',
        status: 'confirmed',
        symptoms: ['chest pain', 'shortness of breath'],
        notes: 'Regular cardiology checkup',
        aiAnalysis: {
          riskLevel: 'medium',
          recommendedSpecialty: 'Cardiology',
          confidence: 85
        }
      },
      {
        user: createdPatients[1]._id,
        doctor: createdDoctors[1]._id,
        appointmentDate: new Date('2024-01-16T14:00:00Z'),
        appointmentTime: '2:00 PM',
        type: 'consultation',
        status: 'pending',
        symptoms: ['fever', 'cough'],
        notes: 'Child wellness visit',
        aiAnalysis: {
          riskLevel: 'low',
          recommendedSpecialty: 'Pediatrics',
          confidence: 90
        }
      },
      {
        user: createdPatients[2]._id,
        doctor: createdDoctors[2]._id,
        appointmentDate: new Date('2024-01-17T11:30:00Z'),
        appointmentTime: '11:30 AM',
        type: 'consultation',
        status: 'completed',
        symptoms: ['skin rash', 'itching'],
        notes: 'Dermatology consultation for skin condition',
        aiAnalysis: {
          riskLevel: 'low',
          recommendedSpecialty: 'Dermatology',
          confidence: 78
        }
      },
      {
        user: createdPatients[3]._id,
        doctor: createdDoctors[3]._id,
        appointmentDate: new Date('2024-01-18T09:00:00Z'),
        appointmentTime: '9:00 AM',
        type: 'consultation',
        status: 'confirmed',
        symptoms: ['knee pain', 'stiffness'],
        notes: 'Orthopedic evaluation for knee issues',
        aiAnalysis: {
          riskLevel: 'medium',
          recommendedSpecialty: 'Orthopedics',
          confidence: 82
        }
      },
      {
        user: createdPatients[4]._id,
        doctor: createdDoctors[4]._id,
        appointmentDate: new Date('2024-01-19T15:00:00Z'),
        appointmentTime: '3:00 PM',
        type: 'consultation',
        status: 'cancelled',
        symptoms: ['headache', 'dizziness'],
        notes: 'Neurological assessment',
        aiAnalysis: {
          riskLevel: 'medium',
          recommendedSpecialty: 'Neurology',
          confidence: 88
        }
      }
    ];

    const createdAppointments = await Appointment.insertMany(appointments);
    console.log(`✅ Created ${createdAppointments.length} appointments`);

    // 5. Create Sample AI Logs
    const aiLogs = [
      {
        user: createdPatients[0]._id,
        symptoms: ['chest pain', 'shortness of breath', 'fatigue'],
        analysis: {
          possibleConditions: [
            { condition: 'Angina', probability: 65 },
            { condition: 'Heart Attack', probability: 30 },
            { condition: 'Anxiety', probability: 20 }
          ],
          riskLevel: 'high',
          recommendedSpecialty: 'Cardiology',
          urgency: 'urgent',
          confidence: 85
        },
        recommendations: [
          'Seek immediate medical attention',
          'Monitor blood pressure',
          'Avoid strenuous activity'
        ],
        doctorRecommendations: [createdDoctors[0]._id],
        timestamp: new Date('2024-01-14T08:30:00Z')
      },
      {
        user: createdPatients[1]._id,
        symptoms: ['fever', 'cough', 'runny nose'],
        analysis: {
          possibleConditions: [
            { condition: 'Common Cold', probability: 70 },
            { condition: 'Flu', probability: 25 },
            { condition: 'COVID-19', probability: 15 }
          ],
          riskLevel: 'low',
          recommendedSpecialty: 'General Practice',
          urgency: 'routine',
          confidence: 90
        },
        recommendations: [
          'Rest and stay hydrated',
          'Monitor temperature',
          'Consider over-the-counter medications'
        ],
        doctorRecommendations: [createdDoctors[1]._id],
        timestamp: new Date('2024-01-15T12:15:00Z')
      },
      {
        user: createdPatients[2]._id,
        symptoms: ['skin rash', 'itching', 'redness'],
        analysis: {
          possibleConditions: [
            { condition: 'Eczema', probability: 60 },
            { condition: 'Allergic Reaction', probability: 35 },
            { condition: 'Contact Dermatitis', probability: 40 }
          ],
          riskLevel: 'low',
          recommendedSpecialty: 'Dermatology',
          urgency: 'routine',
          confidence: 78
        },
        recommendations: [
          'Avoid known allergens',
          'Use gentle skincare products',
          'Apply moisturizer regularly'
        ],
        doctorRecommendations: [createdDoctors[2]._id],
        timestamp: new Date('2024-01-16T16:45:00Z')
      },
      {
        user: createdPatients[3]._id,
        symptoms: ['knee pain', 'swelling', 'stiffness'],
        analysis: {
          possibleConditions: [
            { condition: 'Arthritis', probability: 50 },
            { condition: 'Meniscus Tear', probability: 30 },
            { condition: 'Ligament Strain', probability: 35 }
          ],
          riskLevel: 'medium',
          recommendedSpecialty: 'Orthopedics',
          urgency: 'routine',
          confidence: 82
        },
        recommendations: [
          'Apply ice to reduce swelling',
          'Avoid high-impact activities',
          'Consider physical therapy'
        ],
        doctorRecommendations: [createdDoctors[3]._id],
        timestamp: new Date('2024-01-17T10:20:00Z')
      },
      {
        user: createdPatients[4]._id,
        symptoms: ['headache', 'dizziness', 'nausea'],
        analysis: {
          possibleConditions: [
            { condition: 'Migraine', probability: 55 },
            { condition: 'Tension Headache', probability: 40 },
            { condition: 'Vestibular Disorder', probability: 25 }
          ],
          riskLevel: 'medium',
          recommendedSpecialty: 'Neurology',
          urgency: 'routine',
          confidence: 88
        },
        recommendations: [
          'Rest in a dark, quiet room',
          'Stay hydrated',
          'Track headache triggers'
        ],
        doctorRecommendations: [createdDoctors[4]._id],
        timestamp: new Date('2024-01-18T14:10:00Z')
      }
    ];

    const createdAILogs = await AILog.insertMany(aiLogs);
    console.log(`✅ Created ${createdAILogs.length} AI logs`);

    console.log('\n🎉 Database seeding completed successfully!');
    console.log('=====================================');
    console.log('Summary of created data:');
    console.log(`👥 Patients: ${createdPatients.length}`);
    console.log(`👨‍⚕️ Doctor Users: ${createdDoctorUsers.length}`);
    console.log(`🏥 Doctor Profiles: ${createdDoctors.length}`);
    console.log(`📅 Appointments: ${createdAppointments.length}`);
    console.log(`🤖 AI Logs: ${createdAILogs.length}`);
    console.log('=====================================');
    console.log('\nYou can now view the admin dashboard with real data!');
    console.log('Login as admin and navigate to /admin/dashboard');

  } catch (error) {
    console.error('Error seeding database:', error);
  } finally {
    // Close database connection
    await mongoose.connection.close();
    console.log('Database connection closed');
  }
};

// Run the seeding script
seedDatabase(); 