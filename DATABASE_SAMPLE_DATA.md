# Sample Database Data Guide

## Overview
This guide describes the sample data that has been populated in your healthcare application database to test the admin dashboard functionality.

## 🚀 Quick Start

### 1. Create Sample Data
```bash
cd server
npm run seed:data
```

### 2. Login Credentials

#### Admin Account
- **Email**: `<EMAIL>`
- **Password**: `Admin123!`
- **Role**: Admin (Full access to admin dashboard)

#### Sample Patients
- **<PERSON>**: `<EMAIL>` / `password123`
- **<PERSON>**: `<EMAIL>` / `password123`
- **<PERSON>**: `micha<PERSON>.<EMAIL>` / `password123`
- **<PERSON>**: `<EMAIL>` / `password123`
- **<PERSON>**: `<EMAIL>` / `password123`

#### Sample Doctors
- **Dr. <PERSON>** (Cardiology): `dr.and<PERSON>@hospital.com` / `password123`
- **Dr. <PERSON>** (Pediatrics): `dr.mart<PERSON><PERSON>@hospital.com` / `password123`
- **Dr. <PERSON>** (Dermatology): `<EMAIL>` / `password123`
- **Dr. <PERSON>** (Orthopedics): `<EMAIL>` / `password123`
- **Dr. <PERSON>** (Neurology): `<EMAIL>` / `password123`

## 📊 What Data Was Created

### 1. **5 Patient Records**
- Complete patient profiles with addresses, contact information
- Diverse demographics and medical backgrounds
- All accounts are active and email verified

### 2. **5 Doctor Profiles**
- Complete doctor profiles with specializations
- Education, certifications, and experience details
- Availability schedules and consultation fees
- All doctors are verified and accepting patients

### 3. **5 Appointments**
- Mix of appointment statuses: confirmed, scheduled, completed, cancelled
- Different appointment types and urgency levels
- Realistic symptoms and patient notes
- AI analysis with confidence scores and recommendations
- Fee structures and payment information

### 4. **5 AI Analysis Logs**
- Detailed symptom analysis with AI predictions
- Specialty recommendations with confidence scores
- Request/response metadata and performance metrics
- Various medical categories: emergency, routine, complex, specialized

## 🎯 Admin Dashboard Features to Test

### Dashboard Overview
- **Total Users**: See real count of patients and doctors
- **Active Doctors**: View verified doctors accepting patients
- **Appointment Statistics**: Real appointment data with different statuses
- **AI Analysis Metrics**: Actual AI interaction data

### User Management
- View and manage all 5 patients
- Edit patient information
- Activate/deactivate accounts
- View patient appointment history

### Doctor Management
- View all 5 doctor profiles
- Review doctor verification status
- Manage doctor availability
- View doctor performance metrics

### Appointment Management
- Monitor all 5 appointments
- Filter by status (confirmed, scheduled, completed, cancelled)
- View appointment details and patient notes
- Track appointment fees and payments

### AI Analytics
- Review AI analysis logs
- Monitor AI prediction accuracy
- Track specialty recommendations
- View system performance metrics

## 🔄 Data Refresh

### Reset Sample Data
To clear and recreate sample data:
```bash
cd server
npm run seed:data
```

### Add More Data
The seeding script clears existing data (except admin) and creates fresh sample data. You can run it multiple times to reset the database for testing.

## 🛡️ Security Notes

- All sample passwords are set to `password123`
- In production, use strong, unique passwords
- The admin account password is `Admin123!`
- All sample data is for testing purposes only

## 📈 Testing Scenarios

### User Registration Flow
1. Test patient registration with new accounts
2. Verify doctor application process
3. Test admin approval workflows

### Appointment Booking
1. Login as a patient
2. Book appointments with different doctors
3. Test appointment confirmation process
4. View appointment history

### AI Analysis
1. Use symptom checker as a patient
2. Review AI recommendations
3. Book appointments based on AI suggestions
4. Monitor AI analytics in admin dashboard

### Admin Operations
1. Login as admin
2. Review dashboard statistics
3. Manage user accounts
4. Monitor system health
5. Review AI performance metrics

## 🎉 Ready to Use!

Your healthcare application now has realistic sample data to test all admin dashboard features. You can:

1. **Login as admin** to view the populated dashboard
2. **Test user management** with real patient/doctor data
3. **Monitor appointments** with various statuses and details
4. **Review AI analytics** with actual analysis logs
5. **Experience the full admin workflow** with meaningful data

The admin dashboard should now display real statistics, charts, and data instead of placeholder information! 