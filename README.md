# Medical Appointment System

A full-stack web application for managing medical appointments with three user roles: <PERSON><PERSON>, Doctor, and Admin. Features AI-powered symptom analysis for doctor specialty recommendations.

## 🏗️ Architecture

- **Frontend**: React with Vite
- **Backend**: Node.js with Express
- **Database**: MongoDB
- **AI Integration**: OpenRouter API for symptom analysis
- **Authentication**: JWT-based

## 🚀 Features

### 👤 Patient Features
- User registration and login
- Symptom input with AI-powered specialty suggestions
- Browse doctors by specialty
- Book appointments
- View appointment and symptom history
- Profile management

### 👨‍⚕️ Doctor Features
- Secure login
- View upcoming appointments
- Access patient symptom descriptions
- Accept/reject appointment requests
- Manage availability
- Profile management

### 🛠️ Admin Features
- Admin dashboard
- Manage doctor accounts
- View all users and appointments
- Monitor AI usage logs
- System analytics

## 📁 Project Structure

```
medical-appointment-system/
├── frontend/          # React + Vite frontend
├── server/           # Node.js + Express backend
└── README.md
```

## 🔧 Setup Instructions

### Prerequisites
- Node.js (v18 or higher)
- MongoDB account
- OpenRouter API key

### Installation

1. Clone the repository
2. Install frontend dependencies:
   ```bash
   cd frontend
   npm install
   ```
3. Install backend dependencies:
   ```bash
   cd server
   npm install
   ```
4. Set up environment variables (see individual folder READMEs)
5. Run the application:
   ```bash
   # Terminal 1 - Backend
   cd server
   npm run dev
   
   # Terminal 2 - Frontend
   cd frontend
   npm run dev
   ```

## 🔐 Environment Variables

See the respective folders for detailed environment variable setup:
- `frontend/.env` - Frontend configuration
- `server/.env` - Backend configuration including MongoDB URL and OpenRouter API key

## 📡 API Endpoints

Detailed API documentation available in the server folder.

## 🤖 AI Integration

The system uses OpenRouter's AI API to analyze patient symptoms and suggest the most appropriate doctor specialty. All predictions are logged for admin review.

## 🔒 Security

- JWT-based authentication
- Role-based access control
- Input validation and sanitization
- Secure password hashing

## 👥 User Roles

1. **Patient**: Can register, input symptoms, book appointments
2. **Doctor**: Can manage appointments and view patient information
3. **Admin**: Can manage the entire system and view analytics

## 🛠️ Development

This project is built with modern web technologies focusing on:
- Responsive design
- Clean architecture
- Secure authentication
- Real-time updates
- Comprehensive error handling

## 📄 License

This project is for educational purposes. 