# Admin Dashboard Access Guide

## 🚀 Quick Start Instructions

### 1. Start the Backend Server
```bash
# Navigate to server directory
cd server

# Install dependencies (if not already done)
npm install

# Start the server in development mode
npm run dev
```
**Expected Output:**
```
✅ Database Connected: MongoDB connected successfully
🚀 Server running on port 5000
🔗 API Routes loaded successfully
```

### 2. Start the Frontend Server
```bash
# Open a new terminal and navigate to frontend directory
cd frontend

# Install dependencies (if not already done)
npm install

# Start the frontend development server
npm run dev
```
**Expected Output:**
```
✅ Local:   http://localhost:5173/
✅ Network: use --host to expose
```

### 3. Access the Admin Dashboard

#### Step 1: Open the Application
- Open your browser and go to: `http://localhost:5173`

#### Step 2: Login as Admin
- Click on "Login" or navigate to the login page
- Use the admin credentials:
  - **Email**: `<EMAIL>`
  - **Password**: `Admin123!`

#### Step 3: Navigate to Admin Dashboard
- After successful login, you should be redirected to the admin dashboard
- If not automatically redirected, go to: `http://localhost:5173/admin/dashboard`

## 📊 What You Should See

### Dashboard Statistics (Real Data)
- **Total Users**: 11 (5 patients + 5 doctors + 1 admin)
- **Active Doctors**: 5 verified doctors
- **Total Appointments**: 5 sample appointments
- **AI Analyses**: 5 AI interaction logs

### Real Data Sections
1. **System Health**: Shows operational status
2. **Recent Activity**: Displays actual user registrations, appointments, and AI interactions
3. **Pending Actions**: Shows real pending appointments and system metrics
4. **System Alerts**: Displays system status with real numbers

## 🔧 Troubleshooting

### Issue: "Failed to load dashboard data"
**Cause**: Backend server not running or API connection issues
**Solution**:
1. Make sure backend server is running on port 5000
2. Check that frontend can connect to backend
3. Check browser console for specific error messages

### Issue: Login fails with admin credentials
**Cause**: Admin user not created
**Solution**:
```bash
cd server
npm run seed:admin
```

### Issue: Dashboard shows zeros or no data
**Cause**: Sample data not seeded
**Solution**:
```bash
cd server
npm run seed:data
```

### Issue: API requests failing (CORS errors)
**Cause**: Frontend proxy not configured
**Solution**: Make sure `frontend/vite.config.js` has the proxy configuration (already updated)

### Issue: "Cannot find module" errors
**Cause**: Dependencies not installed
**Solution**:
```bash
# In server directory
cd server && npm install

# In frontend directory  
cd frontend && npm install
```

## 🎯 Testing the Dashboard

### 1. Verify Data Display
- Check that all stat cards show real numbers (not zeros)
- Verify recent activity shows meaningful messages
- Confirm system alerts display actual system status

### 2. Test Dashboard Functionality
- Click "Refresh Data" button to reload dashboard
- Verify console logs show successful API calls
- Check that loading states work properly

### 3. Check Browser Console
Press F12 and look for:
- **Success logs**: "Dashboard data updated successfully"
- **API responses**: Should show actual data structures
- **No errors**: Red error messages indicate issues

## 📱 Additional Test Accounts

### Sample Patients (for testing user management)
- `<EMAIL>` / `password123`
- `<EMAIL>` / `password123`
- `<EMAIL>` / `password123`

### Sample Doctors (for testing doctor management)
- `<EMAIL>` / `password123` (Cardiology)
- `<EMAIL>` / `password123` (Pediatrics)
- `<EMAIL>` / `password123` (Dermatology)

## 🔄 Reset/Refresh Data

### To completely refresh sample data:
```bash
cd server
npm run seed:data
```
This will:
- Clear existing sample data (keeps admin account)
- Create fresh set of 5 patients, 5 doctors, 5 appointments, 5 AI logs
- Reset all statistics and metrics

### To create additional admin users:
```bash
cd server
npm run seed:admin
```

## 📋 Verification Checklist

- [ ] Backend server running on port 5000
- [ ] Frontend server running on port 5173
- [ ] Admin login works with `<EMAIL>` / `Admin123!`
- [ ] Dashboard shows real data (not zeros)
- [ ] Recent activity displays meaningful messages
- [ ] Pending actions show actual counts
- [ ] System alerts show operational status
- [ ] Browser console shows no errors
- [ ] API calls succeed (check Network tab)

## 🎉 Success!

If all checks pass, your admin dashboard is now fully functional with real sample data! You can:

1. **Monitor System Statistics**: View real user counts, appointments, and AI interactions
2. **Track Recent Activity**: See actual system events and user actions
3. **Manage Pending Tasks**: Review real pending appointments and system metrics
4. **System Health Monitoring**: Check operational status and alerts

The dashboard is now ready for full testing and development! 