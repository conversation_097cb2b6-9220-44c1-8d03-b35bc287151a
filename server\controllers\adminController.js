﻿import User from '../models/User.js';
import Appointment from '../models/Appointment.js';
import AILog from '../models/AILog.js';
import { catchAsync } from '../utils/catchAsync.js';
import { AppError } from '../utils/appError.js';

// Get admin dashboard with system overview
export const getAdminDashboard = catchAsync(async (req, res) => {
    // Get user statistics
    const totalUsers = await User.countDocuments();
    const activeUsers = await User.countDocuments({ isActive: true });
    const newUsersThisMonth = await User.countDocuments({
        createdAt: { $gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1) }
    });

    // Get appointment statistics
    const totalAppointments = await Appointment.countDocuments();
    const pendingAppointments = await Appointment.countDocuments({ status: 'pending' });
    const completedAppointments = await Appointment.countDocuments({ status: 'completed' });
    const todayAppointments = await Appointment.countDocuments({
        appointmentDate: {
            $gte: new Date(new Date().setHours(0, 0, 0, 0)),
            $lt: new Date(new Date().setHours(23, 59, 59, 999))
        }
    });

    // Get AI usage statistics
    const totalAIInteractions = await AILog.countDocuments();
    const aiInteractionsToday = await AILog.countDocuments({
        createdAt: {
            $gte: new Date(new Date().setHours(0, 0, 0, 0)),
            $lt: new Date(new Date().setHours(23, 59, 59, 999))
        }
    });

    res.status(200).json({
        status: 'success',
        data: {
            users: {
                total: totalUsers,
                active: activeUsers,
                newThisMonth: newUsersThisMonth
            },
            appointments: {
                total: totalAppointments,
                pending: pendingAppointments,
                completed: completedAppointments,
                today: todayAppointments
            },
            ai: {
                totalInteractions: totalAIInteractions,
                interactionsToday: aiInteractionsToday
            }
        }
    });
});

// Get detailed system analytics
export const getSystemAnalytics = catchAsync(async (req, res) => {
    const { period = '30' } = req.query;
    const days = parseInt(period);
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    // User registration trends
    const userTrends = await User.aggregate([
        {
            $match: { createdAt: { $gte: startDate } }
        },
        {
            $group: {
                _id: { $dateToString: { format: "%Y-%m-%d", date: "$createdAt" } },
                count: { $sum: 1 }
            }
        },
        { $sort: { _id: 1 } }
    ]);

    // Appointment trends
    const appointmentTrends = await Appointment.aggregate([
        {
            $match: { createdAt: { $gte: startDate } }
        },
        {
            $group: {
                _id: { $dateToString: { format: "%Y-%m-%d", date: "$createdAt" } },
                count: { $sum: 1 }
            }
        },
        { $sort: { _id: 1 } }
    ]);

    // AI usage trends
    const aiTrends = await AILog.aggregate([
        {
            $match: { createdAt: { $gte: startDate } }
        },
        {
            $group: {
                _id: { $dateToString: { format: "%Y-%m-%d", date: "$createdAt" } },
                count: { $sum: 1 }
            }
        },
        { $sort: { _id: 1 } }
    ]);

    res.status(200).json({
        status: 'success',
        data: {
            period: days,
            trends: {
                users: userTrends,
                appointments: appointmentTrends,
                ai: aiTrends
            }
        }
    });
});

// Get all users with pagination
export const getAllUsers = catchAsync(async (req, res) => {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    const users = await User.find()
        .select('-password')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit);

    const total = await User.countDocuments();

    res.status(200).json({
        status: 'success',
        results: users.length,
        data: {
            users,
            pagination: {
                page,
                limit,
                total,
                pages: Math.ceil(total / limit)
            }
        }
    });
});

// Get all appointments with pagination and filtering
export const getAllAppointments = catchAsync(async (req, res) => {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;
    const { status, date } = req.query;

    let filter = {};
    if (status) filter.status = status;
    if (date) {
        const startDate = new Date(date);
        const endDate = new Date(date);
        endDate.setDate(endDate.getDate() + 1);
        filter.appointmentDate = { $gte: startDate, $lt: endDate };
    }

    const appointments = await Appointment.find(filter)
        .populate('user', 'name email')
        .sort({ appointmentDate: -1 })
        .skip(skip)
        .limit(limit);

    const total = await Appointment.countDocuments(filter);

    res.status(200).json({
        status: 'success',
        results: appointments.length,
        data: {
            appointments,
            pagination: {
                page,
                limit,
                total,
                pages: Math.ceil(total / limit)
            }
        }
    });
});

// Update user status (activate/deactivate)
export const updateUserStatus = catchAsync(async (req, res) => {
    const { userId } = req.params;
    const { isActive } = req.body;

    const user = await User.findByIdAndUpdate(
        userId,
        { isActive },
        { new: true, runValidators: true }
    ).select('-password');

    if (!user) {
        return next(new AppError('User not found', 404));
    }

    res.status(200).json({
        status: 'success',
        data: {
            user
        }
    });
});

// Update appointment status
export const updateAppointmentStatus = catchAsync(async (req, res) => {
    const { appointmentId } = req.params;
    const { status } = req.body;

    const appointment = await Appointment.findByIdAndUpdate(
        appointmentId,
        { status },
        { new: true, runValidators: true }
    ).populate('user', 'name email');

    if (!appointment) {
        return next(new AppError('Appointment not found', 404));
    }

    res.status(200).json({
        status: 'success',
        data: {
            appointment
        }
    });
});

// Get AI interaction logs
export const getAILogs = catchAsync(async (req, res) => {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    const logs = await AILog.find()
        .populate('user', 'name email')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit);

    const total = await AILog.countDocuments();

    res.status(200).json({
        status: 'success',
        results: logs.length,
        data: {
            logs,
            pagination: {
                page,
                limit,
                total,
                pages: Math.ceil(total / limit)
            }
        }
    });
});
