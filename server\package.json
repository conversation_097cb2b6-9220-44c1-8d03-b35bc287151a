{"name": "medical-appointment-server", "version": "1.0.0", "description": "Backend server for Medical Appointment System", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "seed:admin": "node scripts/seedAdmin.js", "seed:data": "node scripts/seedSampleData.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["medical", "appointment", "healthcare", "node", "express"], "author": "", "license": "ISC", "dependencies": {"axios": "^1.6.2", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.0.3", "morgan": "^1.10.0", "nodemon": "^3.0.2"}}