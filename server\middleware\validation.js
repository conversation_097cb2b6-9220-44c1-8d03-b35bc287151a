import { body, param, query } from 'express-validator';

// User registration validation
export const validateRegistration = [
  body('firstName')
    .notEmpty()
    .withMessage('First name is required')
    .isLength({ min: 2, max: 50 })
    .withMessage('First name must be between 2 and 50 characters')
    .matches(/^[a-zA-Z\s]+$/)
    .withMessage('First name can only contain letters and spaces'),

  body('lastName')
    .notEmpty()
    .withMessage('Last name is required')
    .isLength({ min: 2, max: 50 })
    .withMessage('Last name must be between 2 and 50 characters')
    .matches(/^[a-zA-Z\s]+$/)
    .withMessage('Last name can only contain letters and spaces'),

  body('email')
    .notEmpty()
    .withMessage('Email is required')
    .isEmail()
    .withMessage('Please provide a valid email')
    .normalizeEmail(),

  body('password')
    .notEmpty()
    .withMessage('Password is required')
    .isLength({ min: 6 })
    .withMessage('Password must be at least 6 characters long')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('Password must contain at least one uppercase letter, one lowercase letter, and one number'),

  body('role')
    .optional()
    .isIn(['patient', 'doctor', 'admin'])
    .withMessage('Role must be either patient, doctor, or admin'),

  body('phone')
    .optional()
    .isMobilePhone()
    .withMessage('Please provide a valid phone number'),

  body('dateOfBirth')
    .optional()
    .isDate()
    .withMessage('Please provide a valid date of birth')
    .custom((value) => {
      const birthDate = new Date(value);
      const today = new Date();
      const age = today.getFullYear() - birthDate.getFullYear();
      
      if (age < 13 || age > 120) {
        throw new Error('Age must be between 13 and 120 years');
      }
      return true;
    }),

  body('gender')
    .optional()
    .isIn(['male', 'female', 'other'])
    .withMessage('Gender must be male, female, or other'),

  body('address.street')
    .optional()
    .isLength({ max: 100 })
    .withMessage('Street address cannot exceed 100 characters'),

  body('address.city')
    .optional()
    .isLength({ max: 50 })
    .withMessage('City cannot exceed 50 characters'),

  body('address.state')
    .optional()
    .isLength({ max: 50 })
    .withMessage('State cannot exceed 50 characters'),

  body('address.zipCode')
    .optional()
    .matches(/^\d{5}(-\d{4})?$/)
    .withMessage('Please provide a valid ZIP code'),

  body('address.country')
    .optional()
    .isLength({ max: 50 })
    .withMessage('Country cannot exceed 50 characters')
];

// User login validation
export const validateLogin = [
  body('email')
    .notEmpty()
    .withMessage('Email is required')
    .isEmail()
    .withMessage('Please provide a valid email')
    .normalizeEmail(),

  body('password')
    .notEmpty()
    .withMessage('Password is required')
    .isLength({ min: 6 })
    .withMessage('Password must be at least 6 characters long')
];

// Update profile validation
export const validateUpdateProfile = [
  body('firstName')
    .optional()
    .isLength({ min: 2, max: 50 })
    .withMessage('First name must be between 2 and 50 characters')
    .matches(/^[a-zA-Z\s]+$/)
    .withMessage('First name can only contain letters and spaces'),

  body('lastName')
    .optional()
    .isLength({ min: 2, max: 50 })
    .withMessage('Last name must be between 2 and 50 characters')
    .matches(/^[a-zA-Z\s]+$/)
    .withMessage('Last name can only contain letters and spaces'),

  body('phone')
    .optional()
    .isMobilePhone()
    .withMessage('Please provide a valid phone number'),

  body('dateOfBirth')
    .optional()
    .isDate()
    .withMessage('Please provide a valid date of birth')
    .custom((value) => {
      const birthDate = new Date(value);
      const today = new Date();
      const age = today.getFullYear() - birthDate.getFullYear();
      
      if (age < 13 || age > 120) {
        throw new Error('Age must be between 13 and 120 years');
      }
      return true;
    }),

  body('gender')
    .optional()
    .isIn(['male', 'female', 'other'])
    .withMessage('Gender must be male, female, or other'),

  body('avatar')
    .optional()
    .isURL()
    .withMessage('Avatar must be a valid URL'),

  body('address.street')
    .optional()
    .isLength({ max: 100 })
    .withMessage('Street address cannot exceed 100 characters'),

  body('address.city')
    .optional()
    .isLength({ max: 50 })
    .withMessage('City cannot exceed 50 characters'),

  body('address.state')
    .optional()
    .isLength({ max: 50 })
    .withMessage('State cannot exceed 50 characters'),

  body('address.zipCode')
    .optional()
    .matches(/^\d{5}(-\d{4})?$/)
    .withMessage('Please provide a valid ZIP code'),

  body('address.country')
    .optional()
    .isLength({ max: 50 })
    .withMessage('Country cannot exceed 50 characters')
];

// Change password validation
export const validateChangePassword = [
  body('currentPassword')
    .notEmpty()
    .withMessage('Current password is required'),

  body('newPassword')
    .notEmpty()
    .withMessage('New password is required')
    .isLength({ min: 6 })
    .withMessage('New password must be at least 6 characters long')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('New password must contain at least one uppercase letter, one lowercase letter, and one number'),

  body('confirmPassword')
    .notEmpty()
    .withMessage('Please confirm your new password')
    .custom((value, { req }) => {
      if (value !== req.body.newPassword) {
        throw new Error('New password and confirm password do not match');
      }
      return true;
    })
];

// Doctor registration validation
export const validateDoctorRegistration = [
  body('specialties')
    .isArray({ min: 1 })
    .withMessage('At least one specialty is required')
    .custom((specialties) => {
      const validSpecialties = [
        'General Practice', 'Cardiology', 'Dermatology', 'Endocrinology',
        'Gastroenterology', 'Hematology', 'Infectious Disease', 'Nephrology',
        'Neurology', 'Oncology', 'Orthopedics', 'Pediatrics', 'Psychiatry',
        'Pulmonology', 'Radiology', 'Rheumatology', 'Urology', 'Gynecology',
        'Ophthalmology', 'ENT', 'Emergency Medicine', 'Anesthesiology',
        'Pathology', 'Surgery', 'Plastic Surgery', 'Neurosurgery',
        'Orthopedic Surgery', 'Cardiac Surgery'
      ];
      
      const invalidSpecialties = specialties.filter(s => !validSpecialties.includes(s));
      if (invalidSpecialties.length > 0) {
        throw new Error(`Invalid specialties: ${invalidSpecialties.join(', ')}`);
      }
      return true;
    }),

  body('licenseNumber')
    .notEmpty()
    .withMessage('License number is required')
    .isLength({ min: 5, max: 20 })
    .withMessage('License number must be between 5 and 20 characters'),

  body('yearsOfExperience')
    .notEmpty()
    .withMessage('Years of experience is required')
    .isInt({ min: 0, max: 50 })
    .withMessage('Years of experience must be between 0 and 50'),

  body('consultationFee')
    .notEmpty()
    .withMessage('Consultation fee is required')
    .isFloat({ min: 0 })
    .withMessage('Consultation fee must be a positive number'),

  body('education')
    .isArray({ min: 1 })
    .withMessage('At least one education record is required'),

  body('education.*.degree')
    .notEmpty()
    .withMessage('Degree is required for each education record'),

  body('education.*.institution')
    .notEmpty()
    .withMessage('Institution is required for each education record'),

  body('education.*.year')
    .notEmpty()
    .withMessage('Year is required for each education record')
    .isInt({ min: 1950, max: new Date().getFullYear() })
    .withMessage('Year must be between 1950 and current year'),

  body('bio')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('Bio cannot exceed 1000 characters'),

  body('languages')
    .optional()
    .isArray()
    .withMessage('Languages must be an array')
];

// Appointment validation
export const validateAppointment = [
  body('doctor')
    .notEmpty()
    .withMessage('Doctor is required')
    .isMongoId()
    .withMessage('Doctor must be a valid ID'),

  body('appointmentDate')
    .notEmpty()
    .withMessage('Appointment date is required')
    .isDate()
    .withMessage('Please provide a valid appointment date')
    .custom((value) => {
      const appointmentDate = new Date(value);
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      
      if (appointmentDate < today) {
        throw new Error('Appointment date cannot be in the past');
      }
      
      const maxDate = new Date();
      maxDate.setDate(maxDate.getDate() + 90); // 90 days in advance
      
      if (appointmentDate > maxDate) {
        throw new Error('Appointment date cannot be more than 90 days in advance');
      }
      
      return true;
    }),

  body('appointmentTime')
    .notEmpty()
    .withMessage('Appointment time is required')
    .matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/)
    .withMessage('Please provide a valid time in HH:MM format'),

  body('symptoms')
    .notEmpty()
    .withMessage('Symptoms description is required')
    .isLength({ min: 10, max: 1000 })
    .withMessage('Symptoms description must be between 10 and 1000 characters'),

  body('appointmentType')
    .optional()
    .isIn(['consultation', 'follow-up', 'checkup', 'emergency'])
    .withMessage('Appointment type must be consultation, follow-up, checkup, or emergency'),

  body('duration')
    .optional()
    .isInt({ min: 15, max: 120 })
    .withMessage('Duration must be between 15 and 120 minutes'),

  body('patientNotes')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Patient notes cannot exceed 500 characters')
];

// Symptom analysis validation
export const validateSymptomAnalysis = [
  body('symptoms')
    .notEmpty()
    .withMessage('Symptoms description is required')
    .isLength({ min: 10, max: 1000 })
    .withMessage('Symptoms description must be between 10 and 1000 characters')
    .custom((value) => {
      // Basic check for medical emergency keywords
      const emergencyKeywords = [
        'chest pain', 'difficulty breathing', 'severe headache', 'unconscious',
        'bleeding heavily', 'heart attack', 'stroke', 'seizure', 'overdose'
      ];
      
      const lowerSymptoms = value.toLowerCase();
      const hasEmergencyKeywords = emergencyKeywords.some(keyword => 
        lowerSymptoms.includes(keyword)
      );
      
      if (hasEmergencyKeywords) {
        throw new Error('For emergency symptoms, please call 911 or go to the nearest emergency room immediately');
      }
      
      return true;
    })
];

// Pagination validation
export const validatePagination = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),

  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),

  query('sort')
    .optional()
    .isIn(['createdAt', '-createdAt', 'name', '-name', 'date', '-date'])
    .withMessage('Sort must be one of: createdAt, -createdAt, name, -name, date, -date')
];

// MongoDB ObjectId validation
export const validateObjectId = (paramName) => [
  param(paramName)
    .isMongoId()
    .withMessage(`${paramName} must be a valid MongoDB ObjectId`)
];

// Date range validation
export const validateDateRange = [
  query('startDate')
    .optional()
    .isDate()
    .withMessage('Start date must be a valid date'),

  query('endDate')
    .optional()
    .isDate()
    .withMessage('End date must be a valid date')
    .custom((value, { req }) => {
      if (req.query.startDate && value) {
        const startDate = new Date(req.query.startDate);
        const endDate = new Date(value);
        
        if (endDate <= startDate) {
          throw new Error('End date must be after start date');
        }
      }
      return true;
    })
];

// Search validation
export const validateSearch = [
  query('search')
    .optional()
    .isLength({ min: 1, max: 100 })
    .withMessage('Search query must be between 1 and 100 characters')
    .trim()
];

// Rating validation
export const validateRating = [
  body('rating')
    .notEmpty()
    .withMessage('Rating is required')
    .isInt({ min: 1, max: 5 })
    .withMessage('Rating must be between 1 and 5'),

  body('comment')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Comment cannot exceed 500 characters')
];

// Status validation
export const validateStatus = [
  body('status')
    .notEmpty()
    .withMessage('Status is required')
    .isIn(['pending', 'confirmed', 'cancelled', 'completed', 'no-show'])
    .withMessage('Status must be one of: pending, confirmed, cancelled, completed, no-show')
];

// Role validation
export const validateRole = [
  body('role')
    .notEmpty()
    .withMessage('Role is required')
    .isIn(['patient', 'doctor', 'admin'])
    .withMessage('Role must be one of: patient, doctor, admin')
];

// Export all validators
export default {
  validateRegistration,
  validateLogin,
  validateUpdateProfile,
  validateChangePassword,
  validateDoctorRegistration,
  validateAppointment,
  validateSymptomAnalysis,
  validatePagination,
  validateObjectId,
  validateDateRange,
  validateSearch,
  validateRating,
  validateStatus,
  validateRole
}; 