import { useState, useEffect } from 'react';
import { useAuth } from '../../context/AuthContext';
import { 
  UserIcon, 
  CameraIcon,
  EnvelopeIcon,
  PhoneIcon,
  MapPinIcon,
  AcademicCapIcon,
  ShieldCheckIcon,
  StarIcon,
  ClockIcon,
  CurrencyDollarIcon,
  DocumentTextIcon,
  PencilIcon,
  CheckIcon,
  XMarkIcon,
  EyeIcon,
  EyeSlashIcon,
  PlusIcon,
  TrashIcon
} from '@heroicons/react/24/outline';

export default function DoctorProfile() {
  const { user } = useAuth();
  const [profile, setProfile] = useState(null);
  const [loading, setLoading] = useState(true);
  const [editing, setEditing] = useState(false);
  const [editedProfile, setEditedProfile] = useState(null);
  const [showPasswordSection, setShowPasswordSection] = useState(false);
  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });

  useEffect(() => {
    fetchProfile();
  }, []);

  const fetchProfile = async () => {
    setLoading(true);
    try {
      // Mock data - replace with actual API calls
      const mockProfile = {
        id: user?.id || 1,
        personalInfo: {
          firstName: 'Michael',
          lastName: 'Chen',
          email: '<EMAIL>',
          phone: '+****************',
          address: '123 Medical Center Dr, San Francisco, CA 94102',
          dateOfBirth: '1985-08-15',
          gender: 'Male',
          avatar: null
        },
        professionalInfo: {
          licenseNumber: 'MD123456',
          specialties: ['Cardiology', 'Internal Medicine'],
          education: [
            {
              degree: 'MD',
              institution: 'Harvard Medical School',
              year: '2010'
            },
            {
              degree: 'BS Biology',
              institution: 'Stanford University',
              year: '2006'
            }
          ],
          certifications: [
            'Board Certified in Cardiology',
            'Board Certified in Internal Medicine',
            'Advanced Cardiac Life Support (ACLS)'
          ],
          experience: '12 years',
          languages: ['English', 'Mandarin', 'Spanish'],
          hospitalAffiliations: [
            'San Francisco General Hospital',
            'UCSF Medical Center'
          ]
        },
        practiceInfo: {
          consultationFee: 250,
          followUpFee: 150,
          emergencyFee: 400,
          acceptedInsurance: [
            'Blue Cross Blue Shield',
            'Aetna',
            'United Healthcare',
            'Cigna',
            'Medicare'
          ],
          paymentMethods: ['Cash', 'Credit Card', 'Insurance'],
          officeHours: {
            monday: { start: '09:00', end: '17:00', available: true },
            tuesday: { start: '09:00', end: '17:00', available: true },
            wednesday: { start: '09:00', end: '17:00', available: true },
            thursday: { start: '09:00', end: '17:00', available: true },
            friday: { start: '09:00', end: '15:00', available: true },
            saturday: { start: '09:00', end: '13:00', available: true },
            sunday: { start: '', end: '', available: false }
          }
        },
        statistics: {
          totalPatients: 1247,
          totalAppointments: 3456,
          avgRating: 4.8,
          totalReviews: 234,
          completedAppointments: 3201,
          yearsOfPractice: 12
        },
        settings: {
          emailNotifications: true,
          smsNotifications: true,
          appointmentReminders: true,
          marketingEmails: false,
          profileVisibility: 'public'
        }
      };

      setProfile(mockProfile);
      setEditedProfile(mockProfile);
    } catch (error) {
      console.error('Error fetching profile:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    try {
      // Mock API call - replace with actual save logic
      console.log('Saving profile:', editedProfile);
      
      setProfile(editedProfile);
      setEditing(false);
      
      // Show success message
      alert('Profile updated successfully!');
    } catch (error) {
      console.error('Error saving profile:', error);
      alert('Error updating profile. Please try again.');
    }
  };

  const handleCancel = () => {
    setEditedProfile(profile);
    setEditing(false);
  };

  const handlePasswordChange = async (e) => {
    e.preventDefault();
    
    if (passwordData.newPassword !== passwordData.confirmPassword) {
      alert('New passwords do not match');
      return;
    }
    
    try {
      // Mock API call - replace with actual password change logic
      console.log('Changing password');
      
      setPasswordData({
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      });
      setShowPasswordSection(false);
      
      alert('Password changed successfully!');
    } catch (error) {
      console.error('Error changing password:', error);
      alert('Error changing password. Please try again.');
    }
  };

  const addSpecialty = () => {
    const newSpecialty = prompt('Enter new specialty:');
    if (newSpecialty) {
      setEditedProfile(prev => ({
        ...prev,
        professionalInfo: {
          ...prev.professionalInfo,
          specialties: [...prev.professionalInfo.specialties, newSpecialty]
        }
      }));
    }
  };

  const removeSpecialty = (index) => {
    setEditedProfile(prev => ({
      ...prev,
      professionalInfo: {
        ...prev.professionalInfo,
        specialties: prev.professionalInfo.specialties.filter((_, i) => i !== index)
      }
    }));
  };

  const addEducation = () => {
    const degree = prompt('Enter degree:');
    const institution = prompt('Enter institution:');
    const year = prompt('Enter year:');
    
    if (degree && institution && year) {
      setEditedProfile(prev => ({
        ...prev,
        professionalInfo: {
          ...prev.professionalInfo,
          education: [...prev.professionalInfo.education, { degree, institution, year }]
        }
      }));
    }
  };

  const removeEducation = (index) => {
    setEditedProfile(prev => ({
      ...prev,
      professionalInfo: {
        ...prev.professionalInfo,
        education: prev.professionalInfo.education.filter((_, i) => i !== index)
      }
    }));
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Doctor Profile</h1>
              <p className="text-gray-600 mt-2">
                Manage your professional information and settings
              </p>
            </div>
            <div className="flex items-center space-x-4">
              {editing ? (
                <div className="flex space-x-2">
                  <button
                    onClick={handleSave}
                    className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center"
                  >
                    <CheckIcon className="h-4 w-4 mr-2" />
                    Save
                  </button>
                  <button
                    onClick={handleCancel}
                    className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors flex items-center"
                  >
                    <XMarkIcon className="h-4 w-4 mr-2" />
                    Cancel
                  </button>
                </div>
              ) : (
                <button
                  onClick={() => setEditing(true)}
                  className="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 transition-colors flex items-center"
                >
                  <PencilIcon className="h-4 w-4 mr-2" />
                  Edit Profile
                </button>
              )}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Column - Profile Overview */}
          <div className="lg:col-span-1 space-y-6">
            {/* Profile Picture and Basic Info */}
            <div className="bg-white rounded-lg shadow p-6">
              <div className="text-center">
                <div className="relative inline-block">
                  <div className="h-32 w-32 rounded-full bg-indigo-100 flex items-center justify-center mx-auto">
                    <UserIcon className="h-16 w-16 text-indigo-600" />
                  </div>
                  {editing && (
                    <button className="absolute bottom-0 right-0 bg-indigo-600 text-white rounded-full p-2 hover:bg-indigo-700 transition-colors">
                      <CameraIcon className="h-4 w-4" />
                    </button>
                  )}
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mt-4">
                  Dr. {profile.personalInfo.firstName} {profile.personalInfo.lastName}
                </h3>
                <p className="text-gray-600">
                  {profile.professionalInfo.specialties.join(', ')}
                </p>
                <div className="flex items-center justify-center mt-2">
                  <StarIcon className="h-5 w-5 text-yellow-400 fill-current" />
                  <span className="ml-1 text-sm text-gray-600">
                    {profile.statistics.avgRating} ({profile.statistics.totalReviews} reviews)
                  </span>
                </div>
              </div>
            </div>

            {/* Quick Stats */}
            <div className="bg-white rounded-lg shadow p-6">
              <h4 className="text-lg font-semibold text-gray-900 mb-4">Quick Stats</h4>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">Total Patients</span>
                  <span className="font-semibold">{profile.statistics.totalPatients}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">Total Appointments</span>
                  <span className="font-semibold">{profile.statistics.totalAppointments}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">Years of Practice</span>
                  <span className="font-semibold">{profile.statistics.yearsOfPractice}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">Completion Rate</span>
                  <span className="font-semibold">
                    {Math.round((profile.statistics.completedAppointments / profile.statistics.totalAppointments) * 100)}%
                  </span>
                </div>
              </div>
            </div>

            {/* Password Section */}
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center justify-between mb-4">
                <h4 className="text-lg font-semibold text-gray-900">Security</h4>
                <button
                  onClick={() => setShowPasswordSection(!showPasswordSection)}
                  className="text-indigo-600 hover:text-indigo-700 text-sm font-medium"
                >
                  Change Password
                </button>
              </div>
              
              {showPasswordSection && (
                <form onSubmit={handlePasswordChange} className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Current Password
                    </label>
                    <input
                      type="password"
                      value={passwordData.currentPassword}
                      onChange={(e) => setPasswordData(prev => ({ ...prev, currentPassword: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      New Password
                    </label>
                    <input
                      type="password"
                      value={passwordData.newPassword}
                      onChange={(e) => setPasswordData(prev => ({ ...prev, newPassword: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Confirm New Password
                    </label>
                    <input
                      type="password"
                      value={passwordData.confirmPassword}
                      onChange={(e) => setPasswordData(prev => ({ ...prev, confirmPassword: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                      required
                    />
                  </div>
                  <div className="flex space-x-2">
                    <button
                      type="submit"
                      className="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 transition-colors text-sm"
                    >
                      Update Password
                    </button>
                    <button
                      type="button"
                      onClick={() => setShowPasswordSection(false)}
                      className="bg-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-400 transition-colors text-sm"
                    >
                      Cancel
                    </button>
                  </div>
                </form>
              )}
            </div>
          </div>

          {/* Right Column - Detailed Information */}
          <div className="lg:col-span-2 space-y-6">
            {/* Personal Information */}
            <div className="bg-white rounded-lg shadow p-6">
              <h4 className="text-lg font-semibold text-gray-900 mb-4">Personal Information</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    First Name
                  </label>
                  <input
                    type="text"
                    value={editing ? editedProfile.personalInfo.firstName : profile.personalInfo.firstName}
                    onChange={(e) => editing && setEditedProfile(prev => ({
                      ...prev,
                      personalInfo: { ...prev.personalInfo, firstName: e.target.value }
                    }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                    disabled={!editing}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Last Name
                  </label>
                  <input
                    type="text"
                    value={editing ? editedProfile.personalInfo.lastName : profile.personalInfo.lastName}
                    onChange={(e) => editing && setEditedProfile(prev => ({
                      ...prev,
                      personalInfo: { ...prev.personalInfo, lastName: e.target.value }
                    }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                    disabled={!editing}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Email
                  </label>
                  <input
                    type="email"
                    value={editing ? editedProfile.personalInfo.email : profile.personalInfo.email}
                    onChange={(e) => editing && setEditedProfile(prev => ({
                      ...prev,
                      personalInfo: { ...prev.personalInfo, email: e.target.value }
                    }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                    disabled={!editing}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Phone
                  </label>
                  <input
                    type="tel"
                    value={editing ? editedProfile.personalInfo.phone : profile.personalInfo.phone}
                    onChange={(e) => editing && setEditedProfile(prev => ({
                      ...prev,
                      personalInfo: { ...prev.personalInfo, phone: e.target.value }
                    }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                    disabled={!editing}
                  />
                </div>
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Address
                  </label>
                  <input
                    type="text"
                    value={editing ? editedProfile.personalInfo.address : profile.personalInfo.address}
                    onChange={(e) => editing && setEditedProfile(prev => ({
                      ...prev,
                      personalInfo: { ...prev.personalInfo, address: e.target.value }
                    }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                    disabled={!editing}
                  />
                </div>
              </div>
            </div>

            {/* Professional Information */}
            <div className="bg-white rounded-lg shadow p-6">
              <h4 className="text-lg font-semibold text-gray-900 mb-4">Professional Information</h4>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    License Number
                  </label>
                  <input
                    type="text"
                    value={editing ? editedProfile.professionalInfo.licenseNumber : profile.professionalInfo.licenseNumber}
                    onChange={(e) => editing && setEditedProfile(prev => ({
                      ...prev,
                      professionalInfo: { ...prev.professionalInfo, licenseNumber: e.target.value }
                    }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                    disabled={!editing}
                  />
                </div>

                <div>
                  <div className="flex items-center justify-between mb-2">
                    <label className="block text-sm font-medium text-gray-700">
                      Specialties
                    </label>
                    {editing && (
                      <button
                        onClick={addSpecialty}
                        className="text-indigo-600 hover:text-indigo-700 text-sm font-medium"
                      >
                        <PlusIcon className="h-4 w-4 inline mr-1" />
                        Add
                      </button>
                    )}
                  </div>
                  <div className="space-y-2">
                    {(editing ? editedProfile.professionalInfo.specialties : profile.professionalInfo.specialties).map((specialty, index) => (
                      <div key={index} className="flex items-center justify-between bg-gray-50 rounded-lg p-2">
                        <span className="text-sm">{specialty}</span>
                        {editing && (
                          <button
                            onClick={() => removeSpecialty(index)}
                            className="text-red-600 hover:text-red-700"
                          >
                            <TrashIcon className="h-4 w-4" />
                          </button>
                        )}
                      </div>
                    ))}
                  </div>
                </div>

                <div>
                  <div className="flex items-center justify-between mb-2">
                    <label className="block text-sm font-medium text-gray-700">
                      Education
                    </label>
                    {editing && (
                      <button
                        onClick={addEducation}
                        className="text-indigo-600 hover:text-indigo-700 text-sm font-medium"
                      >
                        <PlusIcon className="h-4 w-4 inline mr-1" />
                        Add
                      </button>
                    )}
                  </div>
                  <div className="space-y-2">
                    {(editing ? editedProfile.professionalInfo.education : profile.professionalInfo.education).map((edu, index) => (
                      <div key={index} className="flex items-center justify-between bg-gray-50 rounded-lg p-3">
                        <div>
                          <p className="font-medium text-sm">{edu.degree}</p>
                          <p className="text-xs text-gray-600">{edu.institution} • {edu.year}</p>
                        </div>
                        {editing && (
                          <button
                            onClick={() => removeEducation(index)}
                            className="text-red-600 hover:text-red-700"
                          >
                            <TrashIcon className="h-4 w-4" />
                          </button>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            {/* Practice Information */}
            <div className="bg-white rounded-lg shadow p-6">
              <h4 className="text-lg font-semibold text-gray-900 mb-4">Practice Information</h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Consultation Fee ($)
                  </label>
                  <input
                    type="number"
                    value={editing ? editedProfile.practiceInfo.consultationFee : profile.practiceInfo.consultationFee}
                    onChange={(e) => editing && setEditedProfile(prev => ({
                      ...prev,
                      practiceInfo: { ...prev.practiceInfo, consultationFee: parseInt(e.target.value) }
                    }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                    disabled={!editing}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Follow-up Fee ($)
                  </label>
                  <input
                    type="number"
                    value={editing ? editedProfile.practiceInfo.followUpFee : profile.practiceInfo.followUpFee}
                    onChange={(e) => editing && setEditedProfile(prev => ({
                      ...prev,
                      practiceInfo: { ...prev.practiceInfo, followUpFee: parseInt(e.target.value) }
                    }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                    disabled={!editing}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Emergency Fee ($)
                  </label>
                  <input
                    type="number"
                    value={editing ? editedProfile.practiceInfo.emergencyFee : profile.practiceInfo.emergencyFee}
                    onChange={(e) => editing && setEditedProfile(prev => ({
                      ...prev,
                      practiceInfo: { ...prev.practiceInfo, emergencyFee: parseInt(e.target.value) }
                    }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                    disabled={!editing}
                  />
                </div>
              </div>
            </div>

            {/* Settings */}
            <div className="bg-white rounded-lg shadow p-6">
              <h4 className="text-lg font-semibold text-gray-900 mb-4">Settings</h4>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <label className="text-sm font-medium text-gray-700">Email Notifications</label>
                    <p className="text-xs text-gray-500">Receive email notifications for appointments</p>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={editing ? editedProfile.settings.emailNotifications : profile.settings.emailNotifications}
                      onChange={(e) => editing && setEditedProfile(prev => ({
                        ...prev,
                        settings: { ...prev.settings, emailNotifications: e.target.checked }
                      }))}
                      className="sr-only peer"
                      disabled={!editing}
                    />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
                  </label>
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <label className="text-sm font-medium text-gray-700">SMS Notifications</label>
                    <p className="text-xs text-gray-500">Receive SMS notifications for appointments</p>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={editing ? editedProfile.settings.smsNotifications : profile.settings.smsNotifications}
                      onChange={(e) => editing && setEditedProfile(prev => ({
                        ...prev,
                        settings: { ...prev.settings, smsNotifications: e.target.checked }
                      }))}
                      className="sr-only peer"
                      disabled={!editing}
                    />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
                  </label>
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <label className="text-sm font-medium text-gray-700">Appointment Reminders</label>
                    <p className="text-xs text-gray-500">Send reminders to patients before appointments</p>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={editing ? editedProfile.settings.appointmentReminders : profile.settings.appointmentReminders}
                      onChange={(e) => editing && setEditedProfile(prev => ({
                        ...prev,
                        settings: { ...prev.settings, appointmentReminders: e.target.checked }
                      }))}
                      className="sr-only peer"
                      disabled={!editing}
                    />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
                  </label>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 