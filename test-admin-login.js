// Test script to verify admin login
import fetch from 'node-fetch';

const testAdminLogin = async () => {
  try {
    console.log('🔍 Testing admin login...');
    
    const response = await fetch('http://localhost:5000/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'password123'
      })
    });

    const data = await response.json();
    
    console.log('📊 Response Status:', response.status);
    console.log('📋 Response Data:', JSON.stringify(data, null, 2));
    
    if (data.success && data.user) {
      console.log('✅ Login successful!');
      console.log('👤 User Role:', data.user.role);
      console.log('🔑 Token received:', data.token ? 'Yes' : 'No');
      
      // Test admin dashboard access
      console.log('\n🔍 Testing admin dashboard access...');
      const dashboardResponse = await fetch('http://localhost:5000/api/admin/dashboard', {
        headers: {
          'Authorization': `Bear<PERSON> ${data.token}`,
          'Content-Type': 'application/json'
        }
      });
      
      const dashboardData = await dashboardResponse.json();
      console.log('📊 Dashboard Response Status:', dashboardResponse.status);
      console.log('📋 Dashboard Data:', JSON.stringify(dashboardData, null, 2));
      
    } else {
      console.log('❌ Login failed:', data.error || 'Unknown error');
    }
    
  } catch (error) {
    console.error('💥 Error:', error.message);
  }
};

testAdminLogin();
