/* eslint-disable no-unused-vars */
import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  UsersIcon, 
  UserGroupIcon, 
  CalendarIcon, 
  ChartBarIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  XCircleIcon,
  EyeIcon,
  PlusIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  CpuChipIcon,
  ShieldCheckIcon,
  BellAlertIcon
} from '@heroicons/react/24/outline';
import { adminService } from '../../services';

const AdminDashboard = () => {
  const [stats, setStats] = useState({
    totalUsers: 0,
    totalDoctors: 0,
    totalAppointments: 0,
    activeUsers: 0,
    aiAnalyses: 0
  });
  
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [apiLogs, setApiLogs] = useState([]);

  // Add API call logging
  const logApiCall = (message, data = null) => {
    const timestamp = new Date().toLocaleTimeString();
    setApiLogs(prev => [...prev.slice(-9), { timestamp, message, data }]);
    console.log(`[${timestamp}] ${message}`, data);
  };

  const fetchDashboardData = async () => {
    setLoading(true);
    setError(null);
    logApiCall('🔄 Starting dashboard data fetch...');

    try {
      logApiCall('📡 Making API call to admin dashboard...');

      // Fetch main dashboard data using adminService
      const data = await adminService.getAdminDashboard();
      logApiCall('✅ Dashboard data received:', data);

      // Fetch user stats using adminService
      let userStats = null;
      try {
        userStats = await adminService.getUserStats();
        logApiCall('✅ User stats received:', userStats);
      } catch (userError) {
        logApiCall('⚠️ User stats failed:', userError.message);
      }

      // Update stats with real data
      if (data && (data.status === 'success' || data.success)) {
        const dashData = data.data || {};
        const userData = userStats?.data || {};
        
        const newStats = {
          totalUsers: userData.totalUsers || dashData.users?.total || 0,
          totalDoctors: userData.doctorCount || 0,
          totalAppointments: dashData.appointments?.total || 0,
          activeUsers: userData.activeUsers || dashData.users?.active || 0,
          aiAnalyses: dashData.ai?.totalInteractions || 0
        };

        setStats(newStats);
        logApiCall('📊 Stats updated:', newStats);
        setError(null);
      } else {
        throw new Error('Invalid data format received from API');
      }

    } catch (err) {
      const errorMsg = `Failed to fetch dashboard data: ${err.message}`;
      setError(errorMsg);
      logApiCall('❌ Error occurred:', errorMsg);
      
      // Set fallback data to show something
      setStats({
        totalUsers: 11,
        totalDoctors: 5,
        totalAppointments: 5,
        activeUsers: 10,
        aiAnalyses: 5
      });
    } finally {
      setLoading(false);
      logApiCall('🏁 Dashboard fetch completed');
    }
  };

  useEffect(() => {
    logApiCall('🚀 AdminDashboard component mounted');
    fetchDashboardData();
  }, []);

  // Simple loading state
  if (loading) {
    return (
      <div style={{ padding: '40px', textAlign: 'center', fontSize: '18px' }}>
        <div style={{ 
          width: '50px', 
          height: '50px', 
          border: '5px solid #f3f3f3',
          borderTop: '5px solid #3498db',
          borderRadius: '50%',
          animation: 'spin 1s linear infinite',
          margin: '0 auto 20px'
        }}></div>
        <p>Loading Admin Dashboard...</p>
        <style>{`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}</style>
      </div>
    );
  }

  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif', backgroundColor: '#f5f5f5', minHeight: '100vh' }}>
      {/* Header */}
      <div style={{ backgroundColor: 'white', padding: '20px', borderRadius: '8px', marginBottom: '20px', boxShadow: '0 2px 4px rgba(0,0,0,0.1)' }}>
        <h1 style={{ margin: '0 0 10px 0', fontSize: '28px', color: '#333' }}>Admin Dashboard</h1>
        <p style={{ margin: '0', color: '#666' }}>Healthcare System Management Panel</p>
        <button 
          onClick={fetchDashboardData}
          style={{
            marginTop: '10px',
            padding: '10px 20px',
            backgroundColor: '#007bff',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          🔄 Refresh Data
        </button>
      </div>

      {/* Error Display */}
      {error && (
        <div style={{ 
          backgroundColor: '#f8d7da', 
          color: '#721c24', 
          padding: '15px', 
          borderRadius: '8px', 
          marginBottom: '20px',
          border: '1px solid #f1aeb5'
        }}>
          <strong>⚠️ Warning:</strong> {error}
          <br />
          <small>Showing fallback data below. Check browser console for details.</small>
        </div>
      )}

      {/* Stats Grid */}
      <div style={{ 
        display: 'grid', 
        gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', 
        gap: '20px', 
        marginBottom: '30px' 
      }}>
        <StatCard 
          title="Total Users" 
          value={stats.totalUsers} 
          icon="👥" 
          color="#007bff"
          description="All registered users"
        />
        <StatCard 
          title="Active Doctors" 
          value={stats.totalDoctors} 
          icon="👨‍⚕️" 
          color="#28a745"
          description="Verified healthcare providers"
        />
        <StatCard 
          title="Total Appointments" 
          value={stats.totalAppointments} 
          icon="📅" 
          color="#17a2b8"
          description="All scheduled appointments"
        />
        <StatCard 
          title="Active Users" 
          value={stats.activeUsers} 
          icon="🟢" 
          color="#ffc107"
          description="Currently active accounts"
        />
        <StatCard 
          title="AI Analyses" 
          value={stats.aiAnalyses} 
          icon="🤖" 
          color="#6f42c1"
          description="AI interactions completed"
        />
      </div>

      {/* System Status */}
      <div style={{ 
        display: 'grid', 
        gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', 
        gap: '20px',
        marginBottom: '30px'
      }}>
        <div style={{ backgroundColor: 'white', padding: '20px', borderRadius: '8px', boxShadow: '0 2px 4px rgba(0,0,0,0.1)' }}>
          <h3 style={{ margin: '0 0 15px 0', color: '#333' }}>📊 System Overview</h3>
          <div style={{ lineHeight: '1.8' }}>
            <div>🔵 <strong>System Status:</strong> <span style={{color: '#28a745'}}>Operational</span></div>
            <div>📈 <strong>Database:</strong> <span style={{color: '#28a745'}}>Connected</span></div>
            <div>🌐 <strong>API Services:</strong> <span style={{color: '#28a745'}}>Online</span></div>
            <div>⚡ <strong>Performance:</strong> <span style={{color: '#28a745'}}>Optimal</span></div>
          </div>
        </div>

        <div style={{ backgroundColor: 'white', padding: '20px', borderRadius: '8px', boxShadow: '0 2px 4px rgba(0,0,0,0.1)' }}>
          <h3 style={{ margin: '0 0 15px 0', color: '#333' }}>📋 Quick Actions</h3>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '10px' }}>
            <button style={actionButtonStyle}>👥 Manage Users</button>
            <button style={actionButtonStyle}>👨‍⚕️ Manage Doctors</button>
            <button style={actionButtonStyle}>📅 View Appointments</button>
            <button style={actionButtonStyle}>🤖 AI Analytics</button>
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div style={{ backgroundColor: 'white', padding: '20px', borderRadius: '8px', boxShadow: '0 2px 4px rgba(0,0,0,0.1)', marginBottom: '20px' }}>
        <h3 style={{ margin: '0 0 15px 0', color: '#333' }}>🕒 Recent Activity</h3>
        <div style={{ display: 'flex', flexDirection: 'column', gap: '10px' }}>
          <ActivityItem message={`${stats.totalUsers} users are registered in the system`} time="Current" />
          <ActivityItem message={`${stats.totalDoctors} doctors are available for appointments`} time="Active now" />
          <ActivityItem message={`${stats.totalAppointments} appointments have been scheduled`} time="Total count" />
          <ActivityItem message={`${stats.aiAnalyses} AI analyses have been completed`} time="All time" />
          <ActivityItem message="Sample data successfully loaded into database" time="System initialized" />
        </div>
      </div>

      {/* Debug Console */}
      <div style={{ backgroundColor: 'white', padding: '20px', borderRadius: '8px', boxShadow: '0 2px 4px rgba(0,0,0,0.1)' }}>
        <h3 style={{ margin: '0 0 15px 0', color: '#333' }}>🔧 Debug Console</h3>
        <div style={{ 
          backgroundColor: '#f8f9fa', 
          padding: '10px', 
          borderRadius: '4px', 
          maxHeight: '200px', 
          overflowY: 'auto',
          fontSize: '12px',
          fontFamily: 'monospace'
        }}>
          {apiLogs.map((log, index) => (
            <div key={index} style={{ marginBottom: '5px' }}>
              <span style={{ color: '#666' }}>[{log.timestamp}]</span> {log.message}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

// Simple StatCard component
const StatCard = ({ title, value, icon, color, description }) => (
  <div style={{ 
    backgroundColor: 'white', 
    padding: '20px', 
    borderRadius: '8px', 
    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
    borderLeft: `4px solid ${color}`
  }}>
    <div style={{ fontSize: '24px', marginBottom: '10px' }}>{icon}</div>
    <h3 style={{ margin: '0 0 5px 0', fontSize: '16px', color: '#666' }}>{title}</h3>
    <p style={{ margin: '0 0 5px 0', fontSize: '32px', fontWeight: 'bold', color: color }}>{value.toLocaleString()}</p>
    <p style={{ margin: '0', fontSize: '12px', color: '#999' }}>{description}</p>
  </div>
);

// Simple ActivityItem component
const ActivityItem = ({ message, time }) => (
  <div style={{ 
    padding: '10px', 
    backgroundColor: '#f8f9fa', 
    borderRadius: '4px',
    borderLeft: '3px solid #007bff'
  }}>
    <div style={{ fontSize: '14px', color: '#333' }}>{message}</div>
    <div style={{ fontSize: '12px', color: '#666', marginTop: '5px' }}>{time}</div>
  </div>
);

// Action button style
const actionButtonStyle = {
  padding: '10px 15px',
  backgroundColor: '#f8f9fa',
  border: '1px solid #dee2e6',
  borderRadius: '4px',
  cursor: 'pointer',
  fontSize: '14px',
  textAlign: 'left',
  transition: 'background-color 0.2s'
};

export default AdminDashboard; 