/* eslint-disable no-unused-vars */
import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  UsersIcon, 
  UserGroupIcon, 
  CalendarIcon, 
  ChartBarIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  XCircleIcon,
  EyeIcon,
  PlusIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  CpuChipIcon,
  ShieldCheckIcon,
  BellAlertIcon
} from '@heroicons/react/24/outline';

const AdminDashboard = () => {
  const [stats, setStats] = useState({
    totalUsers: 0,
    totalDoctors: 0,
    totalAppointments: 0,
    pendingApprovals: 0,
    activeUsers: 0,
    totalRevenue: 0,
    aiAnalyses: 0,
    systemHealth: 'excellent'
  });

  const [recentActivity, setRecentActivity] = useState([]);
  const [pendingActions, setPendingActions] = useState([]);
  const [systemAlerts, setSystemAlerts] = useState([]);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    // Mock data - replace with actual API calls
    setStats({
      totalUsers: 1248,
      totalDoctors: 89,
      totalAppointments: 2456,
      pendingApprovals: 7,
      activeUsers: 342,
      totalRevenue: 45620,
      aiAnalyses: 3892,
      systemHealth: 'excellent'
    });

    setRecentActivity([
      { id: 1, type: 'user_registration', message: 'New patient registered: John Doe', time: '2 minutes ago', status: 'success' },
      { id: 2, type: 'appointment_booked', message: 'Appointment booked with Dr. Smith', time: '5 minutes ago', status: 'info' },
      { id: 3, type: 'doctor_application', message: 'New doctor application: Dr. Johnson', time: '10 minutes ago', status: 'warning' },
      { id: 4, type: 'ai_analysis', message: 'AI analysis completed for patient Sarah', time: '15 minutes ago', status: 'success' },
      { id: 5, type: 'system_update', message: 'System backup completed successfully', time: '30 minutes ago', status: 'info' }
    ]);

    setPendingActions([
      { id: 1, type: 'doctor_approval', title: 'Doctor Applications', count: 3, priority: 'high' },
      { id: 2, type: 'user_verification', title: 'User Verifications', count: 12, priority: 'medium' },
      { id: 3, type: 'appointment_review', title: 'Appointment Reviews', count: 5, priority: 'low' },
      { id: 4, type: 'ai_model_update', title: 'AI Model Updates', count: 1, priority: 'high' }
    ]);

    setSystemAlerts([
      { id: 1, type: 'warning', message: 'High API usage detected', time: '1 hour ago' },
      { id: 2, type: 'info', message: 'Database maintenance scheduled', time: '2 hours ago' },
      { id: 3, type: 'success', message: 'Security scan completed', time: '4 hours ago' }
    ]);
  };

  const StatCard = ({ title, value, icon: Icon, trend, trendValue, color = 'blue' }) => (
    <motion.div
      whileHover={{ scale: 1.02 }}
      className="bg-white rounded-xl shadow-lg p-6 border border-gray-100"
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <div className={`p-3 rounded-lg bg-${color}-100`}>
            <Icon className={`w-6 h-6 text-${color}-600`} />
          </div>
          <div className="ml-4">
            <p className="text-gray-500 text-sm font-medium">{title}</p>
            <p className="text-2xl font-bold text-gray-900">{value}</p>
          </div>
        </div>
        {trend && (
          <div className={`flex items-center ${trend === 'up' ? 'text-green-600' : 'text-red-600'}`}>
            {trend === 'up' ? (
              <ArrowUpIcon className="w-4 h-4 mr-1" />
            ) : (
              <ArrowDownIcon className="w-4 h-4 mr-1" />
            )}
            <span className="text-sm font-medium">{trendValue}%</span>
          </div>
        )}
      </div>
    </motion.div>
  );

  const ActivityItem = ({ activity }) => {
    const getStatusColor = (status) => {
      switch (status) {
        case 'success': return 'text-green-600 bg-green-100';
        case 'warning': return 'text-yellow-600 bg-yellow-100';
        case 'info': return 'text-blue-600 bg-blue-100';
        default: return 'text-gray-600 bg-gray-100';
      }
    };

    return (
      <div className="flex items-center p-4 hover:bg-gray-50 transition-colors">
        <div className={`w-2 h-2 rounded-full mr-3 ${getStatusColor(activity.status)}`}></div>
        <div className="flex-1">
          <p className="text-sm text-gray-900">{activity.message}</p>
          <p className="text-xs text-gray-500">{activity.time}</p>
        </div>
      </div>
    );
  };

  const ActionCard = ({ action }) => {
    const getPriorityColor = (priority) => {
      switch (priority) {
        case 'high': return 'bg-red-100 text-red-800 border-red-200';
        case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
        case 'low': return 'bg-green-100 text-green-800 border-green-200';
        default: return 'bg-gray-100 text-gray-800 border-gray-200';
      }
    };

    return (
      <motion.div
        whileHover={{ scale: 1.02 }}
        className="bg-white rounded-lg shadow-md p-4 border border-gray-100"
      >
        <div className="flex items-center justify-between">
          <div>
            <h4 className="font-medium text-gray-900">{action.title}</h4>
            <p className="text-sm text-gray-500">{action.count} items pending</p>
          </div>
          <div className={`px-2 py-1 rounded-full text-xs font-medium border ${getPriorityColor(action.priority)}`}>
            {action.priority}
          </div>
        </div>
        <div className="mt-3 flex space-x-2">
          <button className="flex-1 bg-blue-600 text-white py-2 px-3 rounded-md text-sm font-medium hover:bg-blue-700 transition-colors">
            Review
          </button>
          <button className="p-2 text-gray-400 hover:text-gray-600 transition-colors">
            <EyeIcon className="w-4 h-4" />
          </button>
        </div>
      </motion.div>
    );
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Admin Dashboard</h1>
              <p className="text-sm text-gray-500">Manage your medical platform</p>
            </div>
            <div className="flex items-center space-x-4">
              <button className="p-2 text-gray-400 hover:text-gray-600 transition-colors">
                <BellAlertIcon className="w-6 h-6" />
              </button>
              <button className="bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 transition-colors">
                <PlusIcon className="w-4 h-4 mr-2 inline" />
                Quick Action
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <StatCard
            title="Total Users"
            value={stats.totalUsers.toLocaleString()}
            icon={UsersIcon}
            trend="up"
            trendValue="12"
            color="blue"
          />
          <StatCard
            title="Active Doctors"
            value={stats.totalDoctors}
            icon={UserGroupIcon}
            trend="up"
            trendValue="8"
            color="green"
          />
          <StatCard
            title="Total Appointments"
            value={stats.totalAppointments.toLocaleString()}
            icon={CalendarIcon}
            trend="up"
            trendValue="15"
            color="purple"
          />
          <StatCard
            title="AI Analyses"
            value={stats.aiAnalyses.toLocaleString()}
            icon={CpuChipIcon}
            trend="up"
            trendValue="25"
            color="indigo"
          />
        </div>

        {/* System Health & Alerts */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
          <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">System Health</h3>
              <ShieldCheckIcon className="w-6 h-6 text-green-600" />
            </div>
            <div className="text-center">
              <div className="w-16 h-16 mx-auto bg-green-100 rounded-full flex items-center justify-center mb-2">
                <CheckCircleIcon className="w-8 h-8 text-green-600" />
              </div>
              <p className="text-2xl font-bold text-green-600 capitalize">{stats.systemHealth}</p>
              <p className="text-sm text-gray-500">All systems operational</p>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Active Users</h3>
              <ClockIcon className="w-6 h-6 text-blue-600" />
            </div>
            <div className="text-center">
              <p className="text-3xl font-bold text-blue-600">{stats.activeUsers}</p>
              <p className="text-sm text-gray-500">Currently online</p>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Pending Approvals</h3>
              <ExclamationTriangleIcon className="w-6 h-6 text-yellow-600" />
            </div>
            <div className="text-center">
              <p className="text-3xl font-bold text-yellow-600">{stats.pendingApprovals}</p>
              <p className="text-sm text-gray-500">Require attention</p>
            </div>
          </div>
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Recent Activity */}
          <div className="bg-white rounded-xl shadow-lg border border-gray-100">
            <div className="p-6 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">Recent Activity</h3>
            </div>
            <div className="max-h-96 overflow-y-auto">
              {recentActivity.map((activity) => (
                <ActivityItem key={activity.id} activity={activity} />
              ))}
            </div>
          </div>

          {/* Pending Actions */}
          <div className="bg-white rounded-xl shadow-lg border border-gray-100">
            <div className="p-6 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">Pending Actions</h3>
            </div>
            <div className="p-6 space-y-4">
              {pendingActions.map((action) => (
                <ActionCard key={action.id} action={action} />
              ))}
            </div>
          </div>
        </div>

        {/* System Alerts */}
        <div className="mt-8 bg-white rounded-xl shadow-lg border border-gray-100">
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">System Alerts</h3>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              {systemAlerts.map((alert) => (
                <div key={alert.id} className="flex items-center p-4 bg-gray-50 rounded-lg">
                  <div className={`w-2 h-2 rounded-full mr-3 ${
                    alert.type === 'warning' ? 'bg-yellow-500' : 
                    alert.type === 'success' ? 'bg-green-500' : 'bg-blue-500'
                  }`}></div>
                  <div className="flex-1">
                    <p className="text-sm text-gray-900">{alert.message}</p>
                    <p className="text-xs text-gray-500">{alert.time}</p>
                  </div>
                  <button className="text-gray-400 hover:text-gray-600 transition-colors">
                    <XCircleIcon className="w-4 h-4" />
                  </button>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminDashboard; 