import util from 'util';

// ANSI color codes
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  dim: '\x1b[2m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
  bgRed: '\x1b[41m',
  bgGreen: '\x1b[42m',
  bgYellow: '\x1b[43m',
  bgBlue: '\x1b[44m',
  bgMagenta: '\x1b[45m',
  bgCyan: '\x1b[46m'
};

// Helper function to format timestamp
const getTimestamp = () => {
  return new Date().toISOString().replace('T', ' ').replace(/\..+/, '');
};

// Helper function to create consistent log format
const formatLog = (level, message, color, bgColor = '') => {
  const timestamp = getTimestamp();
  const levelStr = level.toUpperCase().padEnd(7);
  
  return `${colors.dim}${timestamp}${colors.reset} ${bgColor}${color}${colors.bright} ${levelStr} ${colors.reset} ${message}`;
};

// Logger class
class Logger {
  constructor() {
    this.isProduction = process.env.NODE_ENV === 'production';
  }

  info(message, ...args) {
    const formattedMessage = args.length > 0 ? util.format(message, ...args) : message;
    console.log(formatLog('INFO', formattedMessage, colors.blue, colors.bgBlue));
  }

  success(message, ...args) {
    const formattedMessage = args.length > 0 ? util.format(message, ...args) : message;
    console.log(formatLog('SUCCESS', formattedMessage, colors.green, colors.bgGreen));
  }

  warn(message, ...args) {
    const formattedMessage = args.length > 0 ? util.format(message, ...args) : message;
    console.log(formatLog('WARN', formattedMessage, colors.yellow, colors.bgYellow));
  }

  error(message, ...args) {
    const formattedMessage = args.length > 0 ? util.format(message, ...args) : message;
    console.log(formatLog('ERROR', formattedMessage, colors.red, colors.bgRed));
  }

  debug(message, ...args) {
    if (!this.isProduction) {
      const formattedMessage = args.length > 0 ? util.format(message, ...args) : message;
      console.log(formatLog('DEBUG', formattedMessage, colors.magenta, colors.bgMagenta));
    }
  }

  // Special methods for server events
  server(message, ...args) {
    const formattedMessage = args.length > 0 ? util.format(message, ...args) : message;
    console.log(formatLog('SERVER', formattedMessage, colors.cyan, colors.bgCyan));
  }

  database(message, ...args) {
    const formattedMessage = args.length > 0 ? util.format(message, ...args) : message;
    console.log(formatLog('DB', formattedMessage, colors.green, colors.bgGreen));
  }

  // Startup banner
  banner() {
    console.log('\n' + colors.bright + colors.cyan + '╔══════════════════════════════════════════════════════════════╗');
    console.log('║                                                              ║');
    console.log('║              🏥 MEDICAL APPOINTMENT SYSTEM 🏥                ║');
    console.log('║                                                              ║');
    console.log('║                    Server Starting Up...                     ║');
    console.log('║                                                              ║');
    console.log('╚══════════════════════════════════════════════════════════════╝' + colors.reset);
    console.log('');
  }

  // Connection success
  connectionSuccess(host, port) {
    console.log('\n' + colors.bright + colors.green + '🎉 CONNECTION SUCCESS 🎉');
    console.log('┌─────────────────────────────────────────────────────────────┐');
    console.log(`│ 🌐 Server running on: ${colors.cyan}http://localhost:${port}${colors.green}         │`);
    console.log(`│ 🔗 Health check: ${colors.cyan}http://localhost:${port}/health${colors.green}     │`);
    console.log(`│ 🗄️  Database: ${colors.cyan}${host}${colors.green}                                 │`);
    console.log(`│ 🚀 Environment: ${colors.cyan}${process.env.NODE_ENV || 'development'}${colors.green}                      │`);
    console.log('└─────────────────────────────────────────────────────────────┘' + colors.reset);
    console.log('');
  }

  // API Routes
  routes(routes) {
    console.log(colors.bright + colors.blue + '📍 API ROUTES LOADED');
    console.log('┌─────────────────────────────────────────────────────────────┐');
    routes.forEach(route => {
      console.log(`│ ${colors.cyan}${route.padEnd(57)}${colors.blue} │`);
    });
    console.log('└─────────────────────────────────────────────────────────────┘' + colors.reset);
    console.log('');
  }
}

// Export singleton instance
const logger = new Logger();
export default logger; 