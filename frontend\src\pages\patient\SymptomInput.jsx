import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  SparklesIcon, 
  ExclamationTriangleIcon,
  CheckCircleIcon,
  UserGroupIcon,
  CalendarIcon,
  XMarkIcon,
  PlusIcon
} from '@heroicons/react/24/outline';

export default function SymptomInput() {
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    symptoms: [],
    severity: '',
    duration: '',
    description: '',
    additionalInfo: ''
  });
  const [currentSymptom, setCurrentSymptom] = useState('');
  const [analysis, setAnalysis] = useState(null);
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});

  const commonSymptoms = [
    'Headache', 'Fever', 'Cough', 'Fatigue', 'Nausea', 'Chest Pain',
    'Shortness of Breath', 'Dizziness', 'Sore Throat', 'Muscle Pain',
    'Abdominal Pain', 'Joint Pain', 'Skin Rash', 'Vomiting', 'Diarrhea'
  ];

  const severityLevels = [
    { value: 'mild', label: 'Mild', description: 'Slight discomfort, doesn\'t interfere with daily activities' },
    { value: 'moderate', label: 'Moderate', description: 'Noticeable discomfort, some interference with activities' },
    { value: 'severe', label: 'Severe', description: 'Significant discomfort, major interference with activities' },
    { value: 'critical', label: 'Critical', description: 'Extreme discomfort, unable to perform normal activities' }
  ];

  const durationOptions = [
    { value: 'hours', label: 'Hours' },
    { value: 'days', label: 'Days' },
    { value: 'weeks', label: 'Weeks' },
    { value: 'months', label: 'Months' }
  ];

  const addSymptom = (symptom) => {
    if (symptom && !formData.symptoms.includes(symptom)) {
      setFormData({
        ...formData,
        symptoms: [...formData.symptoms, symptom]
      });
      setCurrentSymptom('');
    }
  };

  const removeSymptom = (symptomToRemove) => {
    setFormData({
      ...formData,
      symptoms: formData.symptoms.filter(symptom => symptom !== symptomToRemove)
    });
  };

  const validateForm = () => {
    const newErrors = {};
    
    if (formData.symptoms.length === 0) {
      newErrors.symptoms = 'Please add at least one symptom';
    }
    
    if (!formData.severity) {
      newErrors.severity = 'Please select severity level';
    }
    
    if (!formData.duration) {
      newErrors.duration = 'Please select duration';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const analyzeSymptoms = async () => {
    if (!validateForm()) return;
    
    setLoading(true);
    try {
      // Mock AI analysis - replace with actual API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const mockAnalysis = {
        confidence: 85,
        recommendedSpecialty: 'General Practice',
        urgencyLevel: 'medium',
        possibleConditions: [
          { name: 'Upper Respiratory Infection', probability: 75 },
          { name: 'Viral Syndrome', probability: 60 }
        ],
        recommendations: [
          'Rest and stay hydrated',
          'Monitor symptoms for changes',
          'Consider over-the-counter medication'
        ]
      };
      
      setAnalysis(mockAnalysis);
    } catch (error) {
      setErrors({ form: 'Analysis failed. Please try again.' });
    } finally {
      setLoading(false);
    }
  };

  const getUrgencyColor = (level) => {
    switch (level) {
      case 'high':
        return 'text-red-600 bg-red-100 border-red-200';
      case 'medium':
        return 'text-yellow-600 bg-yellow-100 border-yellow-200';
      case 'low':
        return 'text-green-600 bg-green-100 border-green-200';
      default:
        return 'text-gray-600 bg-gray-100 border-gray-200';
    }
  };

  const bookAppointment = (doctorId) => {
    navigate(`/patient/book-appointment/${doctorId}`);
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 flex items-center">
            <SparklesIcon className="h-8 w-8 mr-3 text-purple-600" />
            AI Symptom Analysis
          </h1>
          <p className="text-gray-600 mt-2">
            Describe your symptoms and get personalized doctor recommendations.
          </p>
        </div>

        {!analysis ? (
          <div className="bg-white rounded-lg shadow-lg p-6">
            {/* Symptoms Input */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                What symptoms are you experiencing?
              </label>
              
              {/* Current Symptoms */}
              {formData.symptoms.length > 0 && (
                <div className="flex flex-wrap gap-2 mb-4">
                  {formData.symptoms.map((symptom, index) => (
                    <span 
                      key={index}
                      className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-indigo-100 text-indigo-800"
                    >
                      {symptom}
                      <button
                        type="button"
                        onClick={() => removeSymptom(symptom)}
                        className="ml-2 h-4 w-4 rounded-full inline-flex items-center justify-center text-indigo-400 hover:bg-indigo-200"
                      >
                        <XMarkIcon className="h-3 w-3" />
                      </button>
                    </span>
                  ))}
                </div>
              )}

              {/* Add Custom Symptom */}
              <div className="mb-4">
                <div className="flex">
                  <input
                    type="text"
                    value={currentSymptom}
                    onChange={(e) => setCurrentSymptom(e.target.value)}
                    placeholder="Type a symptom..."
                    className="flex-1 border border-gray-300 rounded-l-md px-3 py-2 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                    onKeyPress={(e) => {
                      if (e.key === 'Enter') {
                        addSymptom(currentSymptom);
                      }
                    }}
                  />
                  <button
                    type="button"
                    onClick={() => addSymptom(currentSymptom)}
                    className="px-4 py-2 border border-l-0 border-gray-300 rounded-r-md bg-indigo-600 text-white hover:bg-indigo-700"
                  >
                    <PlusIcon className="h-5 w-5" />
                  </button>
                </div>
              </div>

              {/* Common Symptoms */}
              <div>
                <p className="text-sm text-gray-600 mb-2">Or select from common symptoms:</p>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                  {commonSymptoms.map((symptom) => (
                    <button
                      key={symptom}
                      type="button"
                      onClick={() => addSymptom(symptom)}
                      disabled={formData.symptoms.includes(symptom)}
                      className={`text-sm px-3 py-2 rounded-md border transition-colors ${
                        formData.symptoms.includes(symptom)
                          ? 'bg-gray-100 text-gray-400 border-gray-200 cursor-not-allowed'
                          : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                      }`}
                    >
                      {symptom}
                    </button>
                  ))}
                </div>
              </div>
              
              {errors.symptoms && (
                <p className="mt-2 text-sm text-red-600">{errors.symptoms}</p>
              )}
            </div>

            {/* Submit Button */}
            <div className="flex justify-end">
              <button
                type="button"
                onClick={analyzeSymptoms}
                disabled={loading}
                className={`inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white ${
                  loading
                    ? 'bg-gray-400 cursor-not-allowed'
                    : 'bg-indigo-600 hover:bg-indigo-700'
                } transition-colors duration-200`}
              >
                {loading ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Analyzing Symptoms...
                  </>
                ) : (
                  <>
                    <SparklesIcon className="h-5 w-5 mr-2" />
                    Analyze Symptoms with AI
                  </>
                )}
              </button>
            </div>
          </div>
        ) : (
          /* Analysis Results */
          <div className="space-y-6">
            <div className="bg-white rounded-lg shadow-lg p-6">
              <h2 className="text-xl font-semibold text-gray-900 flex items-center mb-4">
                <CheckCircleIcon className="h-6 w-6 mr-2 text-green-500" />
                Analysis Complete
              </h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="font-medium text-gray-900 mb-2">Your Symptoms</h3>
                  <div className="flex flex-wrap gap-2">
                    {formData.symptoms.map((symptom, index) => (
                      <span key={index} className="px-2 py-1 bg-gray-100 text-gray-700 rounded text-sm">
                        {symptom}
                      </span>
                    ))}
                  </div>
                </div>
                <div>
                  <h3 className="font-medium text-gray-900 mb-2">Recommended Specialty</h3>
                  <p className="text-indigo-600 font-medium">{analysis.recommendedSpecialty}</p>
                  <p className="text-sm text-gray-600">Confidence: {analysis.confidence}%</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Possible Conditions</h3>
              <div className="space-y-3">
                {analysis.possibleConditions.map((condition, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <span className="font-medium text-gray-900">{condition.name}</span>
                    <span className="text-sm text-gray-600">{condition.probability}% match</span>
                  </div>
                ))}
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Recommendations</h3>
              <ul className="space-y-2">
                {analysis.recommendations.map((recommendation, index) => (
                  <li key={index} className="flex items-start">
                    <CheckCircleIcon className="h-5 w-5 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                    <span className="text-gray-700">{recommendation}</span>
                  </li>
                ))}
              </ul>
            </div>

            <div className="flex justify-between">
              <button
                onClick={() => setAnalysis(null)}
                className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
              >
                Analyze New Symptoms
              </button>
              <button
                onClick={() => navigate('/patient/doctors')}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
              >
                Find Doctors
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
} 