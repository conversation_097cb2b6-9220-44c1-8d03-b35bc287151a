import User from '../models/User.js';
import Appointment from '../models/Appointment.js';
import { catchAsync } from '../utils/catchAsync.js';
import { AppError } from '../utils/appError.js';

// Get appointments based on user role
export const getAppointments = catchAsync(async (req, res) => {
    res.status(200).json({ success: true, data: { appointments: [] } });
});

export const createAppointment = catchAsync(async (req, res) => { res.status(201).json({ success: true, data: { appointment: {} } }); });
export const getAppointmentById = catchAsync(async (req, res) => { res.status(200).json({ success: true, data: { appointment: {} } }); });
