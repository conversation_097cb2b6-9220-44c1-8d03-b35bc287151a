import mongoose from 'mongoose';
import User from '../models/User.js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const seedAdmin = async () => {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/healthcare-app');
    console.log('Connected to MongoDB');

    // Check if admin user already exists
    const existingAdmin = await User.findOne({ email: '<EMAIL>' });
    
    if (existingAdmin) {
      console.log('Admin user already exists!');
      console.log('Email: <EMAIL>');
      console.log('You can use this email to login with the existing password.');
      return;
    }

    // Create admin user
    const adminUser = await User.create({
      firstName: 'System',
      lastName: 'Administrator',
      email: '<EMAIL>',
      password: 'Admin123!',
      role: 'admin',
      phone: '+15550100',
      isActive: true,
      isEmailVerified: true
    });

    console.log('✅ Admin user created successfully!');
    console.log('=====================================');
    console.log('Admin Login Credentials:');
    console.log('Email: <EMAIL>');
    console.log('Password: Admin123!');
    console.log('=====================================');
    console.log('You can now login to the admin panel using these credentials.');

  } catch (error) {
    console.error('Error creating admin user:', error);
  } finally {
    // Close database connection
    await mongoose.connection.close();
    console.log('Database connection closed');
  }
};

// Run the seeding script
seedAdmin(); 