{"name": "postcss-import", "version": "16.1.1", "description": "PostCSS plugin to import CSS files", "keywords": ["css", "postcss", "postcss-plugin", "import", "node modules", "npm"], "author": "<PERSON><PERSON>", "license": "MIT", "repository": "https://github.com/postcss/postcss-import.git", "files": ["index.js", "lib"], "engines": {"node": ">=18.0.0"}, "dependencies": {"postcss-value-parser": "^4.0.0", "read-cache": "^1.0.0", "resolve": "^1.1.7"}, "devDependencies": {"ava": "^6.0.0", "c8": "^10.0.0", "eslint": "^9.28.0", "eslint-config-problems": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "globals": "^16.2.0", "postcss": "^8.0.0", "postcss-scss": "^4.0.0", "prettier": "~3.5.0", "sugarss": "^5.0.0"}, "peerDependencies": {"postcss": "^8.0.0"}, "scripts": {"ci": "eslint . && ava", "lint": "eslint . --fix", "pretest": "npm run lint", "test": "c8 ava"}}