import api from './api';

const adminService = {
  getAdminDashboard: async () => {
    const response = await api.get('/admin/dashboard');
    return response;
  },

  getSystemUsers: async (params = {}) => {
    const queryString = new URLSearchParams(params).toString();
    const response = await api.get(`/auth/users?${queryString}`);
    return response;
  },

  updateUserStatus: async (userId, status) => {
    const response = await api.put(`/auth/users/${userId}/status`, { status });
    return response;
  },

  getDoctorManagement: async (params = {}) => {
    const queryString = new URLSearchParams(params).toString();
    const response = await api.get(`/admin/doctors?${queryString}`);
    return response;
  },

  getDoctorApplications: async (params = {}) => {
    const queryString = new URLSearchParams(params).toString();
    const response = await api.get(`/admin/doctors/applications?${queryString}`);
    return response;
  },

  handleDoctorApplication: async (applicationId, action) => {
    const response = await api.put(`/admin/doctor-applications/${applicationId}/${action}`);
    return response;
  },

  updateDoctorStatus: async (doctorId, status) => {
    const response = await api.put(`/admin/doctors/${doctorId}/status`, { status });
    return response;
  },

  getAppointmentMonitoring: async (params = {}) => {
    const queryString = new URLSearchParams(params).toString();
    const response = await api.get(`/admin/appointments?${queryString}`);
    return response;
  },

  getAppointmentStats: async (params = {}) => {
    const queryString = new URLSearchParams(params).toString();
    const response = await api.get(`/admin/appointments/stats?${queryString}`);
    return response;
  },

  updateAppointmentStatus: async (appointmentId, status) => {
    const response = await api.put(`/admin/appointments/${appointmentId}/status`, { status });
    return response;
  },

  getAISystemLogs: async (params = {}) => {
    const queryString = new URLSearchParams(params).toString();
    const response = await api.get(`/ai/logs?${queryString}`);
    return response;
  }
};

export default adminService; 