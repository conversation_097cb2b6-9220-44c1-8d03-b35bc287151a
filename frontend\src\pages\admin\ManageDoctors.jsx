﻿import React, { useState, useEffect } from 'react';
import { UserIcon, CheckCircleIcon, XCircleIcon, ClockIcon, EyeIcon } from '@heroicons/react/24/outline';
import { motion } from 'framer-motion';
import adminService from '../../services/adminService';

const ManageDoctors = () => {
  const [doctors, setDoctors] = useState([]);
  const [applications, setApplications] = useState([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('doctors');

  useEffect(() => {
    fetchDoctors();
    fetchApplications();
  }, []);

  const fetchDoctors = async () => {
    try {
      const response = await adminService.getDoctorManagement();
      console.log('Doctors API response:', response); // Debug log
      
      // Handle the correct response structure: response.data.doctors
      if (response && response.data && Array.isArray(response.data.doctors)) {
        setDoctors(response.data.doctors);
      } else if (response && Array.isArray(response.data)) {
        setDoctors(response.data);
      } else if (response && Array.isArray(response.doctors)) {
        setDoctors(response.doctors);
      } else {
        console.warn('Unexpected doctors response structure:', response);
        setDoctors([]);
      }
    } catch (error) {
      console.error('Error fetching doctors:', error);
      setDoctors([]); // Ensure doctors is always an array
    } finally {
      setLoading(false);
    }
  };

  const fetchApplications = async () => {
    try {
      const response = await adminService.getDoctorApplications();
      console.log('Applications API response:', response); // Debug log
      
      // Handle the correct response structure: response.data.applications
      if (response && response.data && Array.isArray(response.data.applications)) {
        setApplications(response.data.applications);
      } else if (response && Array.isArray(response.data)) {
        setApplications(response.data);
      } else if (response && Array.isArray(response.applications)) {
        setApplications(response.applications);
      } else {
        console.warn('Unexpected applications response structure:', response);
        setApplications([]);
      }
    } catch (error) {
      console.error('Error fetching applications:', error);
      setApplications([]); // Ensure applications is always an array
    }
  };

  const handleApplication = async (applicationId, action) => {
    try {
      const response = await adminService.handleDoctorApplication(applicationId, action);
      if (response.success) {
        fetchApplications();
        fetchDoctors();
      }
    } catch (error) {
      console.error('Error handling application:', error);
    }
  };

  const updateDoctorStatus = async (doctorId, status) => {
    try {
      const response = await adminService.updateDoctorStatus(doctorId, status);
      if (response.success) {
        fetchDoctors();
      }
    } catch (error) {
      console.error('Error updating doctor status:', error);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'inactive':
      case 'suspended':
        return 'bg-red-100 text-red-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Manage Doctors</h1>
        <p className="text-gray-600">Manage doctor accounts and applications</p>
      </div>

      <div className="mb-6">
        <nav className="flex space-x-8">
          <button
            onClick={() => setActiveTab('doctors')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'doctors'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Active Doctors ({Array.isArray(doctors) ? doctors.length : 0})
          </button>
          <button
            onClick={() => setActiveTab('applications')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'applications'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Applications ({Array.isArray(applications) ? applications.length : 0})
          </button>
        </nav>
      </div>

      {activeTab === 'doctors' && (
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Doctor
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Specialization
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Patients
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {Array.isArray(doctors) && doctors.map((doctor, index) => (
                  <motion.tr
                    key={doctor._id || doctor.user?._id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="hover:bg-gray-50"
                  >
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10">
                          <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                            <UserIcon className="h-6 w-6 text-blue-600" />
                          </div>
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900">
                            {doctor.user?.firstName} {doctor.user?.lastName}
                          </div>
                          <div className="text-sm text-gray-500">{doctor.user?.email}</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {doctor.specialties && doctor.specialties.length > 0 ? doctor.specialties.join(', ') : 'Not specified'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(doctor.user?.isActive ? 'active' : 'inactive')}`}>
                        {doctor.user?.isActive ? 'Active' : 'Inactive'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {doctor.totalPatients || 0}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex space-x-2">
                        <button
                          onClick={() => updateDoctorStatus(doctor.user?._id, doctor.user?.isActive ? 'inactive' : 'active')}
                          className={`px-3 py-1 rounded text-xs font-medium ${
                            doctor.user?.isActive
                              ? 'bg-red-100 text-red-800 hover:bg-red-200'
                              : 'bg-green-100 text-green-800 hover:bg-green-200'
                          }`}
                        >
                          {doctor.user?.isActive ? 'Deactivate' : 'Activate'}
                        </button>
                        <button className="text-blue-600 hover:text-blue-900">
                          <EyeIcon className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </motion.tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {activeTab === 'applications' && (
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Applicant
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Specialization
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Experience
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Applied
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {Array.isArray(applications) && applications.map((application, index) => (
                  <motion.tr
                    key={application._id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="hover:bg-gray-50"
                  >
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10">
                          <div className="h-10 w-10 rounded-full bg-yellow-100 flex items-center justify-center">
                            <ClockIcon className="h-6 w-6 text-yellow-600" />
                          </div>
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900">
                            {application.firstName} {application.lastName}
                          </div>
                          <div className="text-sm text-gray-500">{application.email}</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {application.specialties ? application.specialties.join(', ') : 'Not specified'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {application.experience || 'Not specified'} {application.experience ? 'years' : ''}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(application.createdAt).toLocaleDateString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex space-x-2">
                        <button
                          onClick={() => handleApplication(application._id, 'approve')}
                          className="bg-green-100 text-green-800 hover:bg-green-200 px-3 py-1 rounded text-xs font-medium"
                        >
                          Approve
                        </button>
                        <button
                          onClick={() => handleApplication(application._id, 'reject')}
                          className="bg-red-100 text-red-800 hover:bg-red-200 px-3 py-1 rounded text-xs font-medium"
                        >
                          Reject
                        </button>
                        <button className="text-blue-600 hover:text-blue-900">
                          <EyeIcon className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </motion.tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {((activeTab === 'doctors' && Array.isArray(doctors) && doctors.length === 0) || 
        (activeTab === 'applications' && Array.isArray(applications) && applications.length === 0)) && (
        <div className="text-center py-12">
          <UserIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">
            No {activeTab === 'doctors' ? 'doctors' : 'applications'} found
          </h3>
          <p className="mt-1 text-sm text-gray-500">
            {activeTab === 'doctors' 
              ? 'No doctors are currently registered in the system.'
              : 'No pending doctor applications at this time.'
            }
          </p>
        </div>
      )}
    </div>
  );
};

export default ManageDoctors;
