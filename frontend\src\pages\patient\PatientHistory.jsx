import { useState, useEffect } from 'react';
import { 
  DocumentTextIcon,
  SparklesIcon,
  CalendarIcon,
  UserIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon
} from '@heroicons/react/24/outline';

export default function PatientHistory() {
  const [history, setHistory] = useState({
    appointments: [],
    aiAnalyses: [],
    loading: true
  });
  const [activeTab, setActiveTab] = useState('appointments');

  useEffect(() => {
    fetchHistory();
  }, []);

  const fetchHistory = async () => {
    try {
      // Mock data - replace with actual API call
      const mockHistory = {
        appointments: [
          {
            id: 1,
            date: '2024-07-10',
            time: '10:00 AM',
            doctor: 'Dr. <PERSON>',
            specialty: 'Cardiology',
            type: 'Follow-up',
            status: 'completed',
            diagnosis: 'Hypertension management',
            prescription: 'Lisinopril 10mg daily',
            notes: 'Blood pressure well controlled. Continue current medication.'
          },
          {
            id: 2,
            date: '2024-06-15',
            time: '2:30 PM',
            doctor: 'Dr. <PERSON>',
            specialty: 'General Practice',
            type: 'Consultation',
            status: 'completed',
            diagnosis: 'Annual check-up',
            prescription: 'Multivitamin daily',
            notes: 'Overall health excellent. Maintain current lifestyle.'
          },
          {
            id: 3,
            date: '2024-07-20',
            time: '11:00 AM',
            doctor: 'Dr. Emily Rodriguez',
            specialty: 'Dermatology',
            type: 'Consultation',
            status: 'scheduled',
            diagnosis: '',
            prescription: '',
            notes: ''
          }
        ],
        aiAnalyses: [
          {
            id: 1,
            date: '2024-07-10',
            symptoms: ['headache', 'fatigue', 'dizziness'],
            recommendation: 'Cardiology',
            confidence: 92,
            outcome: 'appointment_booked',
            doctorVisited: 'Dr. Sarah Johnson',
            finalDiagnosis: 'Blood pressure spike'
          },
          {
            id: 2,
            date: '2024-06-20',
            symptoms: ['cough', 'sore throat', 'fever'],
            recommendation: 'General Practice',
            confidence: 85,
            outcome: 'self_resolved',
            doctorVisited: null,
            finalDiagnosis: 'Viral upper respiratory infection'
          },
          {
            id: 3,
            date: '2024-06-05',
            symptoms: ['stomach pain', 'nausea'],
            recommendation: 'Gastroenterology',
            confidence: 78,
            outcome: 'appointment_booked',
            doctorVisited: 'Dr. Michael Chen',
            finalDiagnosis: 'Food poisoning'
          }
        ],
        loading: false
      };
      
      setHistory(mockHistory);
    } catch (error) {
      console.error('Error fetching history:', error);
      setHistory(prev => ({ ...prev, loading: false }));
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return 'text-green-600 bg-green-100';
      case 'scheduled':
        return 'text-blue-600 bg-blue-100';
      case 'cancelled':
        return 'text-red-600 bg-red-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getOutcomeColor = (outcome) => {
    switch (outcome) {
      case 'appointment_booked':
        return 'text-green-600 bg-green-100';
      case 'self_resolved':
        return 'text-blue-600 bg-blue-100';
      case 'monitoring':
        return 'text-yellow-600 bg-yellow-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  if (history.loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Medical History</h1>
          <p className="text-gray-600 mt-2">
            View your past appointments and AI symptom analyses
          </p>
        </div>

        {/* Tabs */}
        <div className="bg-white rounded-lg shadow">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex">
              <button
                onClick={() => setActiveTab('appointments')}
                className={`py-4 px-6 border-b-2 font-medium text-sm ${
                  activeTab === 'appointments'
                    ? 'border-indigo-500 text-indigo-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <CalendarIcon className="h-5 w-5 inline-block mr-2" />
                Appointments ({history.appointments.length})
              </button>
              <button
                onClick={() => setActiveTab('analyses')}
                className={`py-4 px-6 border-b-2 font-medium text-sm ${
                  activeTab === 'analyses'
                    ? 'border-indigo-500 text-indigo-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <SparklesIcon className="h-5 w-5 inline-block mr-2" />
                AI Analyses ({history.aiAnalyses.length})
              </button>
            </nav>
          </div>

          <div className="p-6">
            {activeTab === 'appointments' ? (
              /* Appointments History */
              <div className="space-y-6">
                {history.appointments.length === 0 ? (
                  <p className="text-center text-gray-500 py-8">No appointments found</p>
                ) : (
                  history.appointments.map((appointment) => (
                    <div key={appointment.id} className="border border-gray-200 rounded-lg p-6">
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex items-center space-x-4">
                          <div className="flex-shrink-0">
                            <div className="w-12 h-12 bg-indigo-100 rounded-full flex items-center justify-center">
                              <UserIcon className="h-6 w-6 text-indigo-600" />
                            </div>
                          </div>
                          <div>
                            <h3 className="text-lg font-semibold text-gray-900">{appointment.doctor}</h3>
                            <p className="text-indigo-600">{appointment.specialty}</p>
                            <p className="text-sm text-gray-600">
                              {new Date(appointment.date).toLocaleDateString()} at {appointment.time}
                            </p>
                          </div>
                        </div>
                        <div className="flex flex-col items-end space-y-2">
                          <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(appointment.status)}`}>
                            {appointment.status}
                          </span>
                          <span className="text-sm text-gray-600">{appointment.type}</span>
                        </div>
                      </div>

                      {appointment.status === 'completed' && (
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 pt-4 border-t border-gray-100">
                          <div>
                            <h4 className="font-medium text-gray-900 mb-2">Diagnosis</h4>
                            <p className="text-sm text-gray-600">{appointment.diagnosis}</p>
                          </div>
                          <div>
                            <h4 className="font-medium text-gray-900 mb-2">Prescription</h4>
                            <p className="text-sm text-gray-600">{appointment.prescription}</p>
                          </div>
                          <div>
                            <h4 className="font-medium text-gray-900 mb-2">Notes</h4>
                            <p className="text-sm text-gray-600">{appointment.notes}</p>
                          </div>
                        </div>
                      )}
                    </div>
                  ))
                )}
              </div>
            ) : (
              /* AI Analyses History */
              <div className="space-y-6">
                {history.aiAnalyses.length === 0 ? (
                  <p className="text-center text-gray-500 py-8">No AI analyses found</p>
                ) : (
                  history.aiAnalyses.map((analysis) => (
                    <div key={analysis.id} className="border border-gray-200 rounded-lg p-6">
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex items-center space-x-4">
                          <div className="flex-shrink-0">
                            <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center">
                              <SparklesIcon className="h-6 w-6 text-purple-600" />
                            </div>
                          </div>
                          <div>
                            <h3 className="text-lg font-semibold text-gray-900">
                              AI Symptom Analysis
                            </h3>
                            <p className="text-sm text-gray-600">
                              {new Date(analysis.date).toLocaleDateString()}
                            </p>
                          </div>
                        </div>
                        <div className="flex flex-col items-end space-y-2">
                          <span className={`px-3 py-1 rounded-full text-sm font-medium ${getOutcomeColor(analysis.outcome)}`}>
                            {analysis.outcome.replace('_', ' ')}
                          </span>
                          <span className="text-sm text-gray-600">{analysis.confidence}% confidence</span>
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <h4 className="font-medium text-gray-900 mb-2">Symptoms Reported</h4>
                          <div className="flex flex-wrap gap-2">
                            {analysis.symptoms.map((symptom, index) => (
                              <span key={index} className="px-2 py-1 bg-gray-100 text-gray-700 rounded text-sm">
                                {symptom}
                              </span>
                            ))}
                          </div>
                        </div>
                        <div>
                          <h4 className="font-medium text-gray-900 mb-2">AI Recommendation</h4>
                          <p className="text-indigo-600 font-medium">{analysis.recommendation}</p>
                        </div>
                      </div>

                      {analysis.doctorVisited && (
                        <div className="pt-4 border-t border-gray-100 mt-4">
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                              <h4 className="font-medium text-gray-900 mb-2">Doctor Consulted</h4>
                              <p className="text-sm text-gray-600">{analysis.doctorVisited}</p>
                            </div>
                            <div>
                              <h4 className="font-medium text-gray-900 mb-2">Final Diagnosis</h4>
                              <p className="text-sm text-gray-600">{analysis.finalDiagnosis}</p>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  ))
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
} 