import { useState, useEffect } from 'react';
import { <PERSON> } from 'react-router-dom';
import { 
  CalendarIcon, 
  UserGroupIcon, 
  ClockIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  ChartBarIcon,
  BellIcon,
  DocumentTextIcon,
  CogIcon
} from '@heroicons/react/24/outline';
import { useAuth } from '../../context/AuthContext';

export default function DoctorDashboard() {
  const { user } = useAuth();
  const [dashboardData, setDashboardData] = useState({
    todayAppointments: [],
    pendingRequests: [],
    recentPatients: [],
    stats: {},
    loading: true
  });

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      // Mock data - replace with actual API calls
      setDashboardData({
        todayAppointments: [
          {
            id: 1,
            time: '09:00 AM',
            patient: '<PERSON>',
            type: 'Consultation',
            status: 'confirmed',
            duration: '30 min',
            symptoms: 'Chest pain, shortness of breath',
            isNew: false
          },
          {
            id: 2,
            time: '10:30 AM',
            patient: '<PERSON>',
            type: 'Follow-up',
            status: 'confirmed',
            duration: '20 min',
            symptoms: 'Hypertension management',
            isNew: false
          },
          {
            id: 3,
            time: '02:00 PM',
            patient: '<PERSON> Chen',
            type: 'Consultation',
            status: 'pending',
            duration: '30 min',
            symptoms: 'Headache, fatigue',
            isNew: true
          }
        ],
        pendingRequests: [
          {
            id: 1,
            patient: 'Sarah <PERSON>',
            requestedDate: '2024-07-16',
            requestedTime: '11:00 AM',
            type: 'Urgent Care',
            symptoms: 'Severe abdominal pain',
            priority: 'high',
            aiRecommendation: 'Gastroenterology - 89% confidence'
          },
          {
            id: 2,
            patient: 'David Brown',
            requestedDate: '2024-07-17',
            requestedTime: '03:00 PM',
            type: 'Consultation',
            symptoms: 'Joint pain, stiffness',
            priority: 'medium',
            aiRecommendation: 'Rheumatology - 76% confidence'
          }
        ],
        recentPatients: [
          {
            id: 1,
            name: 'Alice Cooper',
            lastVisit: '2024-07-10',
            condition: 'Diabetes Type 2',
            status: 'stable'
          },
          {
            id: 2,
            name: 'Robert Taylor',
            lastVisit: '2024-07-09',
            condition: 'Hypertension',
            status: 'improving'
          },
          {
            id: 3,
            name: 'Lisa Anderson',
            lastVisit: '2024-07-08',
            condition: 'Migraine',
            status: 'needs_followup'
          }
        ],
        stats: {
          totalPatients: 127,
          appointmentsToday: 8,
          pendingRequests: 5,
          completedToday: 3,
          avgRating: 4.8,
          weeklyRevenue: 2400
        },
        loading: false
      });
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      setDashboardData(prev => ({ ...prev, loading: false }));
    }
  };

  const handleRequestAction = async (requestId, action) => {
    try {
      // Mock API call
      console.log(`${action} request ${requestId}`);
      // Refresh data after action
      await fetchDashboardData();
    } catch (error) {
      console.error('Error handling request:', error);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'confirmed':
        return 'text-green-600 bg-green-100';
      case 'pending':
        return 'text-yellow-600 bg-yellow-100';
      case 'completed':
        return 'text-blue-600 bg-blue-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'high':
        return 'text-red-600 bg-red-100 border-red-200';
      case 'medium':
        return 'text-yellow-600 bg-yellow-100 border-yellow-200';
      case 'low':
        return 'text-green-600 bg-green-100 border-green-200';
      default:
        return 'text-gray-600 bg-gray-100 border-gray-200';
    }
  };

  const getPatientStatusColor = (status) => {
    switch (status) {
      case 'stable':
        return 'text-green-600';
      case 'improving':
        return 'text-blue-600';
      case 'needs_followup':
        return 'text-orange-600';
      case 'critical':
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  };

  if (dashboardData.loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Welcome Section */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">
            Good morning, Dr. {user?.lastName || 'Doctor'}!
          </h1>
          <p className="text-gray-600 mt-2">
            You have {dashboardData.stats.appointmentsToday} appointments today and {dashboardData.stats.pendingRequests} pending requests.
          </p>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Link 
            to="/doctor/appointments" 
            className="bg-gradient-to-r from-blue-500 to-indigo-600 text-white p-6 rounded-lg shadow-lg hover:shadow-xl transition-shadow duration-200"
          >
            <div className="flex items-center">
              <CalendarIcon className="h-8 w-8 mr-4" />
              <div>
                <h3 className="text-lg font-semibold">Appointments</h3>
                <p className="text-blue-100">Manage your schedule</p>
              </div>
            </div>
          </Link>
          
          <Link 
            to="/doctor/availability" 
            className="bg-gradient-to-r from-green-500 to-teal-600 text-white p-6 rounded-lg shadow-lg hover:shadow-xl transition-shadow duration-200"
          >
            <div className="flex items-center">
              <ClockIcon className="h-8 w-8 mr-4" />
              <div>
                <h3 className="text-lg font-semibold">Availability</h3>
                <p className="text-green-100">Set your schedule</p>
              </div>
            </div>
          </Link>
          
          <Link 
            to="/doctor/profile" 
            className="bg-gradient-to-r from-purple-500 to-pink-600 text-white p-6 rounded-lg shadow-lg hover:shadow-xl transition-shadow duration-200"
          >
            <div className="flex items-center">
              <CogIcon className="h-8 w-8 mr-4" />
              <div>
                <h3 className="text-lg font-semibold">Profile</h3>
                <p className="text-purple-100">Update your info</p>
              </div>
            </div>
          </Link>
          
          <div className="bg-gradient-to-r from-orange-500 to-red-600 text-white p-6 rounded-lg shadow-lg">
            <div className="flex items-center">
              <BellIcon className="h-8 w-8 mr-4" />
              <div>
                <h3 className="text-lg font-semibold">Notifications</h3>
                <p className="text-orange-100">{dashboardData.stats.pendingRequests} pending</p>
              </div>
            </div>
          </div>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-6 gap-6 mb-8">
          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center">
              <UserGroupIcon className="h-8 w-8 text-blue-600 mr-4" />
              <div>
                <p className="text-sm text-gray-600">Total Patients</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {dashboardData.stats.totalPatients}
                </p>
              </div>
            </div>
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center">
              <CalendarIcon className="h-8 w-8 text-green-600 mr-4" />
              <div>
                <p className="text-sm text-gray-600">Today's Appointments</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {dashboardData.stats.appointmentsToday}
                </p>
              </div>
            </div>
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center">
              <ClockIcon className="h-8 w-8 text-yellow-600 mr-4" />
              <div>
                <p className="text-sm text-gray-600">Pending Requests</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {dashboardData.stats.pendingRequests}
                </p>
              </div>
            </div>
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center">
              <CheckCircleIcon className="h-8 w-8 text-indigo-600 mr-4" />
              <div>
                <p className="text-sm text-gray-600">Completed Today</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {dashboardData.stats.completedToday}
                </p>
              </div>
            </div>
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center">
              <ChartBarIcon className="h-8 w-8 text-purple-600 mr-4" />
              <div>
                <p className="text-sm text-gray-600">Avg Rating</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {dashboardData.stats.avgRating} ⭐
                </p>
              </div>
            </div>
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center">
              <DocumentTextIcon className="h-8 w-8 text-emerald-600 mr-4" />
              <div>
                <p className="text-sm text-gray-600">Weekly Revenue</p>
                <p className="text-2xl font-semibold text-gray-900">
                  ${dashboardData.stats.weeklyRevenue}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Today's Appointments */}
          <div className="lg:col-span-1 bg-white rounded-lg shadow">
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-semibold text-gray-900 flex items-center">
                  <CalendarIcon className="h-6 w-6 mr-2 text-blue-600" />
                  Today's Schedule
                </h2>
                <Link 
                  to="/doctor/appointments" 
                  className="text-indigo-600 hover:text-indigo-700 text-sm font-medium"
                >
                  View All
                </Link>
              </div>
            </div>
            <div className="p-6">
              {dashboardData.todayAppointments.length === 0 ? (
                <p className="text-gray-500 text-center py-8">
                  No appointments today
                </p>
              ) : (
                <div className="space-y-4">
                  {dashboardData.todayAppointments.map((appointment) => (
                    <div key={appointment.id} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center">
                          <span className="font-semibold text-gray-900">{appointment.time}</span>
                          {appointment.isNew && (
                            <span className="ml-2 px-2 py-1 text-xs font-medium bg-blue-100 text-blue-600 rounded-full">
                              New
                            </span>
                          )}
                        </div>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(appointment.status)}`}>
                          {appointment.status}
                        </span>
                      </div>
                      <h4 className="font-medium text-gray-900">{appointment.patient}</h4>
                      <p className="text-sm text-gray-600">{appointment.type} • {appointment.duration}</p>
                      <p className="text-sm text-gray-600 mt-1">{appointment.symptoms}</p>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Pending Requests */}
          <div className="lg:col-span-1 bg-white rounded-lg shadow">
            <div className="p-6 border-b border-gray-200">
              <h2 className="text-xl font-semibold text-gray-900 flex items-center">
                <BellIcon className="h-6 w-6 mr-2 text-orange-600" />
                Pending Requests
              </h2>
            </div>
            <div className="p-6">
              {dashboardData.pendingRequests.length === 0 ? (
                <p className="text-gray-500 text-center py-8">
                  No pending requests
                </p>
              ) : (
                <div className="space-y-4">
                  {dashboardData.pendingRequests.map((request) => (
                    <div key={request.id} className={`border rounded-lg p-4 ${getPriorityColor(request.priority)}`}>
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-semibold text-gray-900">{request.patient}</h4>
                        <span className="px-2 py-1 rounded-full text-xs font-medium bg-white bg-opacity-70">
                          {request.priority} priority
                        </span>
                      </div>
                      <p className="text-sm text-gray-600 mb-2">
                        {new Date(request.requestedDate).toLocaleDateString()} at {request.requestedTime}
                      </p>
                      <p className="text-sm font-medium text-gray-900 mb-1">{request.type}</p>
                      <p className="text-sm text-gray-600 mb-2">{request.symptoms}</p>
                      <p className="text-xs text-gray-500 mb-3">{request.aiRecommendation}</p>
                      
                      <div className="flex space-x-2">
                        <button
                          onClick={() => handleRequestAction(request.id, 'approve')}
                          className="flex-1 bg-green-600 text-white text-sm py-2 px-3 rounded hover:bg-green-700 transition-colors"
                        >
                          Approve
                        </button>
                        <button
                          onClick={() => handleRequestAction(request.id, 'reject')}
                          className="flex-1 bg-red-600 text-white text-sm py-2 px-3 rounded hover:bg-red-700 transition-colors"
                        >
                          Decline
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Recent Patients */}
          <div className="lg:col-span-1 bg-white rounded-lg shadow">
            <div className="p-6 border-b border-gray-200">
              <h2 className="text-xl font-semibold text-gray-900 flex items-center">
                <UserGroupIcon className="h-6 w-6 mr-2 text-purple-600" />
                Recent Patients
              </h2>
            </div>
            <div className="p-6">
              {dashboardData.recentPatients.length === 0 ? (
                <p className="text-gray-500 text-center py-8">
                  No recent patients
                </p>
              ) : (
                <div className="space-y-4">
                  {dashboardData.recentPatients.map((patient) => (
                    <Link
                      key={patient.id}
                      to={`/doctor/patient/${patient.id}`}
                      className="block border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors"
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-semibold text-gray-900">{patient.name}</h4>
                          <p className="text-sm text-gray-600">{patient.condition}</p>
                          <p className="text-xs text-gray-500">
                            Last visit: {new Date(patient.lastVisit).toLocaleDateString()}
                          </p>
                        </div>
                        <div className="text-right">
                          <span className={`text-sm font-medium ${getPatientStatusColor(patient.status)}`}>
                            {patient.status.replace('_', ' ')}
                          </span>
                        </div>
                      </div>
                    </Link>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 