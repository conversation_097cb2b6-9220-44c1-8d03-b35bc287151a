import mongoose from 'mongoose';
import User from '../models/User.js';
import Doctor from '../models/Doctor.js';
import Appointment from '../models/Appointment.js';
import AILog from '../models/AILog.js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const seedDatabase = async () => {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/healthcare-app');
    console.log('Connected to MongoDB');

    // Clear existing data (optional - comment out if you want to keep existing data)
    console.log('Clearing existing sample data...');
    await User.deleteMany({ email: { $not: /admin@healthcare\.com/ } }); // Keep admin user
    await Doctor.deleteMany({});
    await Appointment.deleteMany({});
    await AILog.deleteMany({});

    console.log('Creating sample data...');

    // 1. Create Sample Patients
    const patients = [
      {
        firstName: '<PERSON>',
        lastName: '<PERSON>',
        email: '<EMAIL>',
        password: 'password123',
        role: 'patient',
        phone: '+15551234567',
        dateOfBirth: new Date('1990-05-15'),
        gender: 'male',
        address: {
          street: '123 Main St',
          city: 'New York',
          state: 'NY',
          zipCode: '10001',
          country: 'United States'
        },
        isActive: true,
        isEmailVerified: true
      },
      {
        firstName: 'Sarah',
        lastName: 'Johnson',
        email: '<EMAIL>',
        password: 'password123',
        role: 'patient',
        phone: '+15551234568',
        dateOfBirth: new Date('1985-09-22'),
        gender: 'female',
        address: {
          street: '456 Oak Ave',
          city: 'Los Angeles',
          state: 'CA',
          zipCode: '90210',
          country: 'United States'
        },
        isActive: true,
        isEmailVerified: true
      },
      {
        firstName: 'Michael',
        lastName: 'Brown',
        email: '<EMAIL>',
        password: 'password123',
        role: 'patient',
        phone: '+15551234569',
        dateOfBirth: new Date('1978-12-03'),
        gender: 'male',
        address: {
          street: '789 Pine Rd',
          city: 'Chicago',
          state: 'IL',
          zipCode: '60601',
          country: 'United States'
        },
        isActive: true,
        isEmailVerified: true
      },
      {
        firstName: 'Emily',
        lastName: 'Davis',
        email: '<EMAIL>',
        password: 'password123',
        role: 'patient',
        phone: '+15551234570',
        dateOfBirth: new Date('1995-07-18'),
        gender: 'female',
        address: {
          street: '321 Elm St',
          city: 'Houston',
          state: 'TX',
          zipCode: '77001',
          country: 'United States'
        },
        isActive: true,
        isEmailVerified: true
      },
      {
        firstName: 'David',
        lastName: 'Wilson',
        email: '<EMAIL>',
        password: 'password123',
        role: 'patient',
        phone: '+15551234571',
        dateOfBirth: new Date('1988-03-25'),
        gender: 'male',
        address: {
          street: '654 Maple Dr',
          city: 'Phoenix',
          state: 'AZ',
          zipCode: '85001',
          country: 'United States'
        },
        isActive: true,
        isEmailVerified: true
      }
    ];

    const createdPatients = [];
    for (const patient of patients) {
      const createdPatient = await User.create(patient);
      createdPatients.push(createdPatient);
    }
    console.log(`✅ Created ${createdPatients.length} patients`);

    // 2. Create Sample Doctor Users
    const doctorUsers = [
      {
        firstName: 'Dr. Robert',
        lastName: 'Anderson',
        email: '<EMAIL>',
        password: 'password123',
        role: 'doctor',
        phone: '+15559876543',
        isActive: true,
        isEmailVerified: true
      },
      {
        firstName: 'Dr. Lisa',
        lastName: 'Martinez',
        email: '<EMAIL>',
        password: 'password123',
        role: 'doctor',
        phone: '+15559876544',
        isActive: true,
        isEmailVerified: true
      },
      {
        firstName: 'Dr. James',
        lastName: 'Taylor',
        email: '<EMAIL>',
        password: 'password123',
        role: 'doctor',
        phone: '+15559876545',
        isActive: true,
        isEmailVerified: true
      },
      {
        firstName: 'Dr. Maria',
        lastName: 'Garcia',
        email: '<EMAIL>',
        password: 'password123',
        role: 'doctor',
        phone: '+15559876546',
        isActive: true,
        isEmailVerified: true
      },
      {
        firstName: 'Dr. William',
        lastName: 'Lee',
        email: '<EMAIL>',
        password: 'password123',
        role: 'doctor',
        phone: '+15559876547',
        isActive: true,
        isEmailVerified: true
      }
    ];

    const createdDoctorUsers = [];
    for (const doctorUser of doctorUsers) {
      const createdDoctorUser = await User.create(doctorUser);
      createdDoctorUsers.push(createdDoctorUser);
    }
    console.log(`✅ Created ${createdDoctorUsers.length} doctor users`);

    // 3. Create Doctor Profiles
    const doctorProfiles = [
      {
        user: createdDoctorUsers[0]._id,
        specialties: ['Cardiology'],
        primarySpecialty: 'Cardiology',
        licenseNumber: 'MD12345',
        yearsOfExperience: 15,
        education: [
          {
            degree: 'MD',
            institution: 'Harvard Medical School',
            year: 2008
          }
        ],
        certifications: [
          {
            name: 'Board Certified Cardiologist',
            issuedBy: 'American Board of Cardiology',
            issuedDate: new Date('2010-01-01')
          },
          {
            name: 'ACLS Certified',
            issuedBy: 'American Heart Association',
            issuedDate: new Date('2020-01-01')
          }
        ],
        bio: 'Experienced cardiologist specializing in heart disease prevention and treatment.',
        availability: [
          {
            day: 'Monday',
            slots: [
              { startTime: '09:00', endTime: '17:00', isAvailable: true }
            ]
          },
          {
            day: 'Tuesday',
            slots: [
              { startTime: '09:00', endTime: '17:00', isAvailable: true }
            ]
          },
          {
            day: 'Wednesday',
            slots: [
              { startTime: '09:00', endTime: '17:00', isAvailable: true }
            ]
          },
          {
            day: 'Thursday',
            slots: [
              { startTime: '09:00', endTime: '17:00', isAvailable: true }
            ]
          },
          {
            day: 'Friday',
            slots: [
              { startTime: '09:00', endTime: '15:00', isAvailable: true }
            ]
          }
        ],
        consultationFee: 200,
        isVerified: true,
        isAcceptingPatients: true
      },
      {
        user: createdDoctorUsers[1]._id,
        specialties: ['Pediatrics'],
        primarySpecialty: 'Pediatrics',
        licenseNumber: 'MD12346',
        yearsOfExperience: 12,
        education: [
          {
            degree: 'MD',
            institution: 'Johns Hopkins University',
            year: 2011
          }
        ],
        certifications: [
          {
            name: 'Board Certified Pediatrician',
            issuedBy: 'American Board of Pediatrics',
            issuedDate: new Date('2013-01-01')
          },
          {
            name: 'PALS Certified',
            issuedBy: 'American Heart Association',
            issuedDate: new Date('2020-01-01')
          }
        ],
        bio: 'Dedicated pediatrician focused on comprehensive child healthcare.',
        availability: [
          {
            day: 'Monday',
            slots: [
              { startTime: '08:00', endTime: '16:00', isAvailable: true }
            ]
          },
          {
            day: 'Tuesday',
            slots: [
              { startTime: '08:00', endTime: '16:00', isAvailable: true }
            ]
          },
          {
            day: 'Wednesday',
            slots: [
              { startTime: '08:00', endTime: '16:00', isAvailable: true }
            ]
          },
          {
            day: 'Thursday',
            slots: [
              { startTime: '08:00', endTime: '16:00', isAvailable: true }
            ]
          },
          {
            day: 'Friday',
            slots: [
              { startTime: '08:00', endTime: '14:00', isAvailable: true }
            ]
          }
        ],
        consultationFee: 150,
        isVerified: true,
        isAcceptingPatients: true
      },
      {
        user: createdDoctorUsers[2]._id,
        specialties: ['Dermatology'],
        primarySpecialty: 'Dermatology',
        licenseNumber: 'MD12347',
        yearsOfExperience: 10,
        education: [
          {
            degree: 'MD',
            institution: 'Stanford University School of Medicine',
            year: 2013
          }
        ],
        certifications: [
          {
            name: 'Board Certified Dermatologist',
            issuedBy: 'American Board of Dermatology',
            issuedDate: new Date('2015-01-01')
          }
        ],
        bio: 'Skilled dermatologist specializing in skin conditions and cosmetic procedures.',
        availability: [
          {
            day: 'Monday',
            slots: [
              { startTime: '10:00', endTime: '18:00', isAvailable: true }
            ]
          },
          {
            day: 'Tuesday',
            slots: [
              { startTime: '10:00', endTime: '18:00', isAvailable: true }
            ]
          },
          {
            day: 'Wednesday',
            slots: [
              { startTime: '10:00', endTime: '18:00', isAvailable: true }
            ]
          },
          {
            day: 'Thursday',
            slots: [
              { startTime: '10:00', endTime: '18:00', isAvailable: true }
            ]
          },
          {
            day: 'Friday',
            slots: [
              { startTime: '10:00', endTime: '16:00', isAvailable: true }
            ]
          }
        ],
        consultationFee: 180,
        isVerified: true,
        isAcceptingPatients: true
      },
      {
        user: createdDoctorUsers[3]._id,
        specialties: ['Orthopedics'],
        primarySpecialty: 'Orthopedics',
        licenseNumber: 'MD12348',
        yearsOfExperience: 18,
        education: [
          {
            degree: 'MD',
            institution: 'Mayo Clinic Alix School of Medicine',
            year: 2005
          }
        ],
        certifications: [
          {
            name: 'Board Certified Orthopedic Surgeon',
            issuedBy: 'American Board of Orthopedic Surgery',
            issuedDate: new Date('2007-01-01')
          }
        ],
        bio: 'Expert orthopedic surgeon with extensive experience in joint replacement.',
        availability: [
          {
            day: 'Monday',
            slots: [
              { startTime: '07:00', endTime: '15:00', isAvailable: true }
            ]
          },
          {
            day: 'Tuesday',
            slots: [
              { startTime: '07:00', endTime: '15:00', isAvailable: true }
            ]
          },
          {
            day: 'Wednesday',
            slots: [
              { startTime: '07:00', endTime: '15:00', isAvailable: true }
            ]
          },
          {
            day: 'Thursday',
            slots: [
              { startTime: '07:00', endTime: '15:00', isAvailable: true }
            ]
          },
          {
            day: 'Friday',
            slots: [
              { startTime: '07:00', endTime: '13:00', isAvailable: true }
            ]
          }
        ],
        consultationFee: 250,
        isVerified: true,
        isAcceptingPatients: true
      },
      {
        user: createdDoctorUsers[4]._id,
        specialties: ['Neurology'],
        primarySpecialty: 'Neurology',
        licenseNumber: 'MD12349',
        yearsOfExperience: 14,
        education: [
          {
            degree: 'MD',
            institution: 'University of California, San Francisco',
            year: 2009
          }
        ],
        certifications: [
          {
            name: 'Board Certified Neurologist',
            issuedBy: 'American Board of Neurology',
            issuedDate: new Date('2011-01-01')
          }
        ],
        bio: 'Neurologist specializing in brain and nervous system disorders.',
        availability: [
          {
            day: 'Monday',
            slots: [
              { startTime: '09:00', endTime: '17:00', isAvailable: true }
            ]
          },
          {
            day: 'Tuesday',
            slots: [
              { startTime: '09:00', endTime: '17:00', isAvailable: true }
            ]
          },
          {
            day: 'Wednesday',
            slots: [
              { startTime: '09:00', endTime: '17:00', isAvailable: true }
            ]
          },
          {
            day: 'Thursday',
            slots: [
              { startTime: '09:00', endTime: '17:00', isAvailable: true }
            ]
          },
          {
            day: 'Friday',
            slots: [
              { startTime: '09:00', endTime: '15:00', isAvailable: true }
            ]
          }
        ],
        consultationFee: 220,
        isVerified: true,
        isAcceptingPatients: true
      }
    ];

    const createdDoctors = await Doctor.insertMany(doctorProfiles);
    console.log(`✅ Created ${createdDoctors.length} doctor profiles`);

    // 4. Create Sample Appointments
    const now = new Date();
    const tomorrow = new Date(now);
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(10, 0, 0, 0); // Set to 10:00 AM
    const dayAfter = new Date(now);
    dayAfter.setDate(dayAfter.getDate() + 2);
    dayAfter.setHours(14, 0, 0, 0); // Set to 2:00 PM

    const appointments = [
      {
        patient: createdPatients[0]._id,
        doctor: createdDoctorUsers[0]._id,
        dateTime: tomorrow,
        appointmentType: 'consultation',
        status: 'confirmed',
        symptoms: 'Experiencing chest pain and shortness of breath during physical activity',
        patientNotes: 'Regular cardiology checkup',
        aiAnalysis: {
          symptoms: 'chest pain, shortness of breath',
          recommendedSpecialty: 'Cardiology',
          confidence: 0.85
        },
        fee: {
          consultation: 200,
          total: 200
        },
        createdBy: createdPatients[0]._id
      },
      {
        patient: createdPatients[1]._id,
        doctor: createdDoctorUsers[1]._id,
        dateTime: dayAfter,
        appointmentType: 'consultation',
        status: 'scheduled',
        symptoms: 'Child has fever and persistent cough for 3 days',
        patientNotes: 'Child wellness visit',
        aiAnalysis: {
          symptoms: 'fever, cough',
          recommendedSpecialty: 'Pediatrics',
          confidence: 0.90
        },
        fee: {
          consultation: 150,
          total: 150
        },
        createdBy: createdPatients[1]._id
      },
      {
        patient: createdPatients[2]._id,
        doctor: createdDoctorUsers[2]._id,
        dateTime: new Date(now.getTime() + 2 * 60 * 60 * 1000), // 2 hours from now
        appointmentType: 'consultation',
        status: 'completed',
        symptoms: 'Skin rash with itching and redness on arms and legs',
        patientNotes: 'Dermatology consultation for skin condition',
        aiAnalysis: {
          symptoms: 'skin rash, itching, redness',
          recommendedSpecialty: 'Dermatology',
          confidence: 0.78
        },
        fee: {
          consultation: 180,
          total: 180
        },
        createdBy: createdPatients[2]._id
      },
      {
        patient: createdPatients[3]._id,
        doctor: createdDoctorUsers[3]._id,
        dateTime: new Date(tomorrow.getTime() - 60 * 60 * 1000), // 9:00 AM tomorrow
        appointmentType: 'consultation',
        status: 'confirmed',
        symptoms: 'Knee pain and stiffness, difficulty walking',
        patientNotes: 'Orthopedic evaluation for knee issues',
        aiAnalysis: {
          symptoms: 'knee pain, stiffness',
          recommendedSpecialty: 'Orthopedics',
          confidence: 0.82
        },
        fee: {
          consultation: 250,
          total: 250
        },
        createdBy: createdPatients[3]._id
      },
      {
        patient: createdPatients[4]._id,
        doctor: createdDoctorUsers[4]._id,
        dateTime: new Date(dayAfter.getTime() + 60 * 60 * 1000), // 3:00 PM day after
        appointmentType: 'consultation',
        status: 'cancelled',
        symptoms: 'Severe headache with dizziness and nausea',
        patientNotes: 'Neurological assessment',
        aiAnalysis: {
          symptoms: 'headache, dizziness, nausea',
          recommendedSpecialty: 'Neurology',
          confidence: 0.88
        },
        fee: {
          consultation: 220,
          total: 220
        },
        createdBy: createdPatients[4]._id,
        cancellationReason: 'Patient requested to reschedule',
        cancelledBy: 'patient',
        cancelledAt: new Date(now.getTime() - 30 * 60 * 1000) // 30 minutes ago
      }
    ];

    const createdAppointments = await Appointment.insertMany(appointments);
    console.log(`✅ Created ${createdAppointments.length} appointments`);

    // 5. Create Sample AI Logs
    const aiLogs = [
      {
        patient: createdPatients[0]._id,
        symptoms: 'Chest pain and shortness of breath during physical activity, along with fatigue',
        aiModel: 'claude-instant',
        modelVersion: '1.0',
        prediction: {
          recommendedSpecialty: 'Cardiology',
          confidence: 0.85,
          alternativeSpecialties: [
            { specialty: 'Emergency Medicine', confidence: 0.30 },
            { specialty: 'General Practice', confidence: 0.20 }
          ],
          urgencyLevel: 'high',
          reasoning: 'Chest pain with shortness of breath during activity suggests possible cardiac issues requiring immediate evaluation.',
          suggestedQuestions: ['How long have you been experiencing these symptoms?', 'Do you have any family history of heart disease?'],
          redFlags: ['Chest pain', 'Shortness of breath during activity']
        },
        requestMetadata: {
          requestId: `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          userAgent: 'Mozilla/5.0 Healthcare App',
          ipAddress: '127.0.0.1',
          sessionId: `session_${Math.random().toString(36).substr(2, 9)}`,
          timestamp: new Date(now.getTime() - 2 * 60 * 60 * 1000) // 2 hours ago
        },
        responseMetadata: {
          responseTime: 1250,
          tokenUsage: {
            inputTokens: 45,
            outputTokens: 120,
            totalTokens: 165
          },
          apiCost: 0.005,
          modelTemperature: 0.7,
          maxTokens: 1000
        },
        status: 'used',
        analytics: {
          category: 'emergency',
          tags: ['cardiac', 'chest-pain', 'urgent']
        }
      },
      {
        patient: createdPatients[1]._id,
        symptoms: 'Fever, persistent cough, and runny nose for 3 days',
        aiModel: 'claude-instant',
        modelVersion: '1.0',
        prediction: {
          recommendedSpecialty: 'Pediatrics',
          confidence: 0.90,
          alternativeSpecialties: [
            { specialty: 'General Practice', confidence: 0.70 },
            { specialty: 'ENT', confidence: 0.25 }
          ],
          urgencyLevel: 'medium',
          reasoning: 'Common cold or flu symptoms in a child, routine pediatric care recommended.',
          suggestedQuestions: ['Has the child been exposed to anyone with similar symptoms?', 'Are there any other symptoms?'],
          redFlags: []
        },
        requestMetadata: {
          requestId: `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          userAgent: 'Mozilla/5.0 Healthcare App',
          ipAddress: '127.0.0.1',
          sessionId: `session_${Math.random().toString(36).substr(2, 9)}`,
          timestamp: new Date(now.getTime() - 4 * 60 * 60 * 1000) // 4 hours ago
        },
        responseMetadata: {
          responseTime: 980,
          tokenUsage: {
            inputTokens: 38,
            outputTokens: 95,
            totalTokens: 133
          },
          apiCost: 0.003,
          modelTemperature: 0.7,
          maxTokens: 1000
        },
        status: 'used',
        analytics: {
          category: 'common',
          tags: ['pediatric', 'respiratory', 'fever']
        }
      },
      {
        patient: createdPatients[2]._id,
        symptoms: 'Skin rash with itching and redness on arms and legs',
        aiModel: 'claude-instant',
        modelVersion: '1.0',
        prediction: {
          recommendedSpecialty: 'Dermatology',
          confidence: 0.78,
          alternativeSpecialties: [
            { specialty: 'General Practice', confidence: 0.35 },
            { specialty: 'Allergology', confidence: 0.40 }
          ],
          urgencyLevel: 'low',
          reasoning: 'Skin condition with typical dermatological symptoms, specialist consultation recommended.',
          suggestedQuestions: ['Have you recently changed soap or detergent?', 'Any new medications or foods?'],
          redFlags: []
        },
        requestMetadata: {
          requestId: `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          userAgent: 'Mozilla/5.0 Healthcare App',
          ipAddress: '127.0.0.1',
          sessionId: `session_${Math.random().toString(36).substr(2, 9)}`,
          timestamp: new Date(now.getTime() - 1 * 60 * 60 * 1000) // 1 hour ago
        },
        responseMetadata: {
          responseTime: 1100,
          tokenUsage: {
            inputTokens: 42,
            outputTokens: 105,
            totalTokens: 147
          },
          apiCost: 0.004,
          modelTemperature: 0.7,
          maxTokens: 1000
        },
        status: 'used',
        analytics: {
          category: 'routine',
          tags: ['dermatology', 'rash', 'allergic-reaction']
        }
      },
      {
        patient: createdPatients[3]._id,
        symptoms: 'Knee pain with swelling and stiffness, difficulty walking',
        aiModel: 'claude-instant',
        modelVersion: '1.0',
        prediction: {
          recommendedSpecialty: 'Orthopedics',
          confidence: 0.82,
          alternativeSpecialties: [
            { specialty: 'Rheumatology', confidence: 0.50 },
            { specialty: 'General Practice', confidence: 0.30 }
          ],
          urgencyLevel: 'medium',
          reasoning: 'Knee pain with swelling suggests possible joint injury or arthritis requiring orthopedic evaluation.',
          suggestedQuestions: ['Did you have any recent injury or trauma?', 'How long have you had these symptoms?'],
          redFlags: []
        },
        requestMetadata: {
          requestId: `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          userAgent: 'Mozilla/5.0 Healthcare App',
          ipAddress: '127.0.0.1',
          sessionId: `session_${Math.random().toString(36).substr(2, 9)}`,
          timestamp: new Date(now.getTime() - 3 * 60 * 60 * 1000) // 3 hours ago
        },
        responseMetadata: {
          responseTime: 1350,
          tokenUsage: {
            inputTokens: 50,
            outputTokens: 130,
            totalTokens: 180
          },
          apiCost: 0.006,
          modelTemperature: 0.7,
          maxTokens: 1000
        },
        status: 'used',
        analytics: {
          category: 'specialized',
          tags: ['orthopedic', 'knee-pain', 'joint-issue']
        }
      },
      {
        patient: createdPatients[4]._id,
        symptoms: 'Severe headache with dizziness and nausea lasting several hours',
        aiModel: 'claude-instant',
        modelVersion: '1.0',
        prediction: {
          recommendedSpecialty: 'Neurology',
          confidence: 0.88,
          alternativeSpecialties: [
            { specialty: 'General Practice', confidence: 0.40 },
            { specialty: 'Emergency Medicine', confidence: 0.25 }
          ],
          urgencyLevel: 'medium',
          reasoning: 'Severe headache with neurological symptoms suggests possible migraine or other neurological condition.',
          suggestedQuestions: ['Do you have a history of migraines?', 'Any visual disturbances or sensitivity to light?'],
          redFlags: ['Severe headache', 'Neurological symptoms']
        },
        requestMetadata: {
          requestId: `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          userAgent: 'Mozilla/5.0 Healthcare App',
          ipAddress: '127.0.0.1',
          sessionId: `session_${Math.random().toString(36).substr(2, 9)}`,
          timestamp: new Date(now.getTime() - 30 * 60 * 1000) // 30 minutes ago
        },
        responseMetadata: {
          responseTime: 1180,
          tokenUsage: {
            inputTokens: 48,
            outputTokens: 125,
            totalTokens: 173
          },
          apiCost: 0.005,
          modelTemperature: 0.7,
          maxTokens: 1000
        },
        status: 'used',
        analytics: {
          category: 'complex',
          tags: ['neurology', 'headache', 'migraine']
        }
      }
    ];

    const createdAILogs = await AILog.insertMany(aiLogs);
    console.log(`✅ Created ${createdAILogs.length} AI logs`);

    console.log('\n🎉 Database seeding completed successfully!');
    console.log('=====================================');
    console.log('Summary of created data:');
    console.log(`👥 Patients: ${createdPatients.length}`);
    console.log(`👨‍⚕️ Doctor Users: ${createdDoctorUsers.length}`);
    console.log(`🏥 Doctor Profiles: ${createdDoctors.length}`);
    console.log(`📅 Appointments: ${createdAppointments.length}`);
    console.log(`🤖 AI Logs: ${createdAILogs.length}`);
    console.log('=====================================');
    console.log('\nSample Login Credentials:');
    console.log('👤 Patients:');
    console.log('  - <EMAIL> / password123');
    console.log('  - <EMAIL> / password123');
    console.log('👨‍⚕️ Doctors:');
    console.log('  - <EMAIL> / password123');
    console.log('  - <EMAIL> / password123');
    console.log('👑 Admin:');
    console.log('  - <EMAIL> / Admin123!');
    console.log('\n✨ You can now view the admin dashboard with real data!');

  } catch (error) {
    console.error('Error seeding database:', error);
  } finally {
    // Close database connection
    await mongoose.connection.close();
    console.log('Database connection closed');
  }
};

// Run the seeding script
seedDatabase(); 