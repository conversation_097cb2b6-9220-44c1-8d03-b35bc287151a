import express from 'express';
import { protect, isDoctor } from '../middleware/auth.js';
import {
  getDoctorDashboard,
  getDoctorAppointments,
  getDoctorPatients,
  getPatientDetails,
  updateAppointmentStatus,
  addAppointmentNotes,
  getDoctorProfile,
  updateDoctorProfile,
  getAvailabilitySettings,
  updateAvailability,
  getDoctorSchedule,
  updateDoctorSchedule
} from '../controllers/doctorController.js';
import {
  validateUpdateProfile,
  validateAppointmentStatus,
  validateAppointmentNotes,
  validateAvailability,
  validateSchedule,
  validatePagination,
  validateObjectId
} from '../middleware/validation.js';

const router = express.Router();

// All routes are protected and require doctor role
router.use(protect);
router.use(isDoctor);

// Dashboard and overview
router.get('/dashboard', getDoctorDashboard);

// Appointments
router.get('/appointments', validatePagination, getDoctorAppointments);
router.put('/appointments/:id/status', validateObjectId('id'), validateAppointmentStatus, updateAppointmentStatus);
router.put('/appointments/:id/notes', validateObjectId('id'), validateAppointmentNotes, addAppointmentNotes);

// Patients
router.get('/patients', validatePagination, getDoctorPatients);
router.get('/patients/:id', validateObjectId('id'), getPatientDetails);

// Profile management
router.get('/profile', getDoctorProfile);
router.put('/profile', validateUpdateProfile, updateDoctorProfile);

// Availability and scheduling
router.get('/availability', getAvailabilitySettings);
router.put('/availability', validateAvailability, updateAvailability);
router.get('/schedule', getDoctorSchedule);
router.put('/schedule', validateSchedule, updateDoctorSchedule);

export default router; 